#!/usr/bin/env python3
"""
企业数据语义匹配系统主程序

提供命令行界面用于构建索引和执行语义搜索
"""

import os
import sys
import time
import click
import json
import shutil
import yaml
from pathlib import Path
from typing import Optional

# 解决OpenMP冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['OMP_NUM_THREADS'] = '1'

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.enhanced_search_engine import EnhancedSemanticSearchEngine
from src.comparison_search_engine import ComparisonSearchEngine
from src.utils import load_config, setup_logging


@click.group()
@click.option('--config', '-c', default='config.yaml', help='配置文件路径')
@click.option('--verbose', '-v', is_flag=True, help='详细输出')
@click.pass_context
def cli(ctx, config, verbose):
    """企业数据语义匹配系统"""
    ctx.ensure_object(dict)
    ctx.obj['config_path'] = config
    ctx.obj['verbose'] = verbose

    # 验证配置文件
    if not os.path.exists(config):
        click.echo(f"错误: 配置文件 {config} 不存在", err=True)
        sys.exit(1)

    # 初始化日志系统
    try:
        config_data = load_config(config)
        setup_logging(config_data)
    except Exception as e:
        click.echo(f"警告: 日志初始化失败: {e}", err=True)


@cli.command()
@click.option('--force', '-f', is_flag=True, help='强制重建索引')
@click.option('--all', is_flag=True, help='构建所有方法的索引/模型')
@click.option('--semantic', is_flag=True, help='只构建语义搜索索引')
@click.option('--tfidf', is_flag=True, help='只构建TF-IDF模型')
@click.option('--exact', is_flag=True, help='只初始化精确匹配搜索引擎')
@click.pass_context
def build_index(ctx, force, all, semantic, tfidf, exact):
    """构建向量索引和搜索模型"""
    config_path = ctx.obj['config_path']

    # 确定要构建的方法
    methods_to_build = []
    if all:
        methods_to_build = ['semantic', 'tfidf', 'exact']
        click.echo("🚀 开始构建所有搜索方法的索引/模型...")
    else:
        if semantic:
            methods_to_build.append('semantic')
        if tfidf:
            methods_to_build.append('tfidf')
        if exact:
            methods_to_build.append('exact')

        # 如果没有指定任何方法，默认构建语义搜索
        if not methods_to_build:
            methods_to_build = ['semantic']
            click.echo("🚀 开始构建语义搜索索引...")
        else:
            method_names = {'semantic': '语义搜索', 'tfidf': 'TF-IDF', 'exact': '精确匹配'}
            selected = [method_names[m] for m in methods_to_build]
            click.echo(f"🚀 开始构建指定方法的索引/模型: {', '.join(selected)}")

    success_count = 0
    total_count = len(methods_to_build)

    try:
        # 1. 构建语义搜索索引
        if 'semantic' in methods_to_build:
            click.echo("\n1️⃣ 构建语义搜索索引...")
            if _build_semantic_index(config_path, force):
                click.echo("   ✅ 语义搜索索引构建成功")
                success_count += 1
            else:
                click.echo("   ❌ 语义搜索索引构建失败")

        # 2. 构建TF-IDF模型
        if 'tfidf' in methods_to_build:
            click.echo("\n2️⃣ 构建TF-IDF模型...")
            if _build_tfidf_model(config_path, force):
                click.echo("   ✅ TF-IDF模型构建成功")
                success_count += 1
            else:
                click.echo("   ❌ TF-IDF模型构建失败")

        # 3. 初始化精确匹配搜索引擎
        if 'exact' in methods_to_build:
            click.echo("\n3️⃣ 初始化精确匹配搜索引擎...")
            if _build_exact_search(config_path, force):
                click.echo("   ✅ 精确匹配搜索引擎初始化成功")
                success_count += 1
            else:
                click.echo("   ❌ 精确匹配搜索引擎初始化失败")

        # 显示总结
        click.echo(f"\n📊 构建完成: {success_count}/{total_count} 个方法成功")

        if success_count == total_count:
            click.echo("🎉 所有指定的索引/模型构建成功!")
        elif success_count > 0:
            click.echo("⚠️ 部分索引/模型构建成功，请检查失败的方法")
            sys.exit(1)
        else:
            click.echo("❌ 所有索引/模型构建失败")
            sys.exit(1)

    except Exception as e:
        click.echo(f"❌ 索引构建过程中发生错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('query')
@click.option('--top-k', '-k', default=10, help='返回结果数量')
@click.option('--threshold', '-t', default=None, help='相似度阈值')
@click.option('--strategy', default='auto',
              type=click.Choice(['auto', 'primary_only', 'weighted_fusion', 'cascade']),
              help='搜索策略')
@click.option('--field-hints', help='字段提示（逗号分隔）')
@click.option('--output', '-o', help='输出文件路径')
@click.option('--format', '-f', default='table',
              type=click.Choice(['table', 'json', 'csv']), help='输出格式')
@click.option('--compare', '-c', is_flag=True, help='启用对比搜索模式（同时使用三种搜索方法）')
@click.option('--display-count', '-d', default=5, help='对比模式下每种方法显示的结果数量')
@click.pass_context
def search(ctx, query, top_k, threshold, strategy, field_hints, output, format, compare, display_count):
    """执行多字段语义搜索或对比搜索"""
    config_path = ctx.obj['config_path']

    if compare:
        # 对比搜索模式
        click.echo(f"🔍 对比搜索: '{query}'")
        click.echo("📊 将同时使用三种搜索方法进行对比")

        try:
            # 初始化对比搜索引擎
            comparison_engine = ComparisonSearchEngine(config_path)

            # 初始化系统
            if not comparison_engine.initialize():
                click.echo("❌ 对比搜索引擎初始化失败", err=True)
                sys.exit(1)

            # 执行对比搜索
            comparison_results = comparison_engine.search_comparison(
                query=query,
                top_k=top_k,
                threshold=threshold
            )

            # 显示对比结果
            comparison_engine.display_comparison_results(comparison_results, display_count)

            # 导出结果（如果指定了输出文件）
            if output:
                comparison_engine.export_comparison_results(comparison_results, output)

        except Exception as e:
            click.echo(f"❌ 对比搜索失败: {e}", err=True)
            import traceback
            traceback.print_exc()
            sys.exit(1)

    else:
        # 标准语义搜索模式（保持原有功能）
        click.echo(f"🔍 搜索: '{query}'")
        if strategy != 'auto':
            click.echo(f"📋 策略: {strategy}")
        if field_hints:
            click.echo(f"🎯 字段提示: {field_hints}")

        try:
            # 初始化增强搜索引擎
            engine = EnhancedSemanticSearchEngine(config_path)

            # 初始化系统
            if not engine.initialize():
                click.echo("❌ 系统初始化失败，请先运行 'python main.py build-index'", err=True)
                sys.exit(1)

            # 处理字段提示
            field_hints_list = None
            if field_hints:
                field_hints_list = [hint.strip() for hint in field_hints.split(',')]

            # 执行搜索
            results = engine.search(
                query=query,
                top_k=top_k,
                threshold=threshold,
                search_strategy=strategy,
                field_hints=field_hints_list
            )

            if not results:
                click.echo("😔 未找到匹配的企业记录")
                return

            # 输出结果
            if format == 'table':
                _display_results_table(results)
            elif format == 'json':
                _display_results_json(results, output)
            elif format == 'csv':
                _export_results_csv(engine, results, output)

        except Exception as e:
            click.echo(f"❌ 搜索失败: {e}", err=True)
            sys.exit(1)


@cli.command()
@click.option('--queries-file', '-f', required=True, help='查询文件路径（每行一个查询）')
@click.option('--top-k', '-k', default=10, help='每个查询的返回结果数量')
@click.option('--threshold', '-t', default=None, help='相似度阈值')
@click.option('--output', '-o', required=True, help='输出文件路径')
@click.pass_context
def batch_search(ctx, queries_file, top_k, threshold, output):
    """批量搜索"""
    config_path = ctx.obj['config_path']
    
    if not os.path.exists(queries_file):
        click.echo(f"❌ 查询文件 {queries_file} 不存在", err=True)
        sys.exit(1)
    
    try:
        # 读取查询
        with open(queries_file, 'r', encoding='utf-8') as f:
            queries = [line.strip() for line in f if line.strip()]
        
        click.echo(f"📋 批量搜索 {len(queries)} 个查询...")

        # 初始化增强搜索引擎
        engine = EnhancedSemanticSearchEngine(config_path)

        # 初始化系统
        if not engine.initialize():
            click.echo("❌ 系统初始化失败，请先运行 'python main.py build-index'", err=True)
            sys.exit(1)

        # 执行批量搜索
        all_results = []
        for query in queries:
            results = engine.search(query, top_k=top_k, threshold=threshold)
            # 为每个结果添加查询信息
            for result in results:
                result['query'] = query
            all_results.append(results)
        
        # 导出结果
        _export_batch_results(all_results, output)
        click.echo(f"✅ 批量搜索完成，结果已保存到 {output}")
        
    except Exception as e:
        click.echo(f"❌ 批量搜索失败: {e}", err=True)
        sys.exit(1)



@cli.command()
@click.pass_context
def test_logging(ctx):
    """测试日志记录功能"""
    config_path = ctx.obj['config_path']

    try:
        # 重新初始化日志（确保配置正确）
        config_data = load_config(config_path)
        setup_logging(config_data)

        # 导入logger
        from src.utils import logger

        click.echo("🧪 测试日志记录功能...")

        # 测试不同级别的日志
        logger.info("This is an INFO level test message")
        logger.warning("This is a WARNING level test message")
        logger.error("This is an ERROR level test message")
        logger.debug("This is a DEBUG level test message")

        # 检查日志文件是否存在
        log_file = config_data.get('logging', {}).get('file', './logs/semantic_search.log')
        if os.path.exists(log_file):
            # 读取最后几行日志
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                last_lines = lines[-5:] if len(lines) >= 5 else lines

            click.echo(f"✅ 日志文件存在: {log_file}")
            click.echo(f"📝 日志文件最后 {len(last_lines)} 行:")
            for line in last_lines:
                click.echo(f"   {line.strip()}")
        else:
            click.echo(f"❌ 日志文件不存在: {log_file}")

        click.echo("✅ 日志测试完成")

    except Exception as e:
        click.echo(f"❌ 日志测试失败: {e}", err=True)


@cli.command()
@click.pass_context
def status(ctx):
    """显示系统状态"""
    config_path = ctx.obj['config_path']
    
    try:
        config = load_config(config_path)
        
        # 检查文件状态
        data_file = config['data']['input_file']
        model_path = config['model']['path']
        cache_dir = config['vector_storage']['cache_dir']

        # 检查多向量索引文件
        multi_index_files = [
            'multi_index_primary.faiss',
            'multi_index_status.faiss',
            'multi_index_date.faiss',
            'multi_index_identifier.faiss'
        ]

        click.echo("📊 系统状态:")
        click.echo(f"  📁 数据文件: {_check_file_status(data_file)}")
        click.echo(f"  🤖 模型目录: {_check_file_status(model_path)}")
        click.echo(f"  � 缓存目录: {_check_file_status(cache_dir)}")

        # 检查多向量索引文件状态
        click.echo("  � 多向量索引文件:")
        all_indexes_exist = True
        for index_file in multi_index_files:
            index_path = os.path.join(cache_dir, index_file)
            status = _check_file_status(index_path)
            click.echo(f"    - {index_file}: {status}")
            if not os.path.exists(index_path):
                all_indexes_exist = False

        # 如果所有索引都存在，显示详细信息
        if all_indexes_exist:
            try:
                engine = EnhancedSemanticSearchEngine(config_path)
                if engine.initialize():
                    status = engine.get_system_status()

                    click.echo("\n🔍 系统详细信息:")
                    click.echo(f"  📊 总记录数: {status['total_records']}")
                    click.echo(f"  � 搜索模式: {status['mode']}")
                    click.echo(f"  �🔢 向量维度: {status['vector_dimensions']}")
                    click.echo(f"  🏗️ 索引类型: {status['index_types']}")
                else:
                    click.echo("\n⚠️ 无法加载索引详细信息")
            except Exception as e:
                click.echo(f"\n⚠️ 获取索引信息失败: {e}")
        
    except Exception as e:
        click.echo(f"❌ 获取状态失败: {e}", err=True)


def _check_file_status(path: str) -> str:
    """检查文件状态"""
    if os.path.exists(path):
        if os.path.isfile(path):
            size_mb = os.path.getsize(path) / (1024 * 1024)
            return f"✅ {path} ({size_mb:.1f} MB)"
        else:
            return f"✅ {path} (目录)"
    else:
        return f"❌ {path} (不存在)"


def _display_results_table(results):
    """以表格形式显示结果"""
    click.echo(f"\n🎯 找到 {len(results)} 个匹配结果:\n")

    for i, result in enumerate(results, 1):
        click.echo(f"【{i}】 相似度: {result['similarity']:.3f}")
        click.echo(f"    企业名称: {result.get('company_name', '未知')}")
        click.echo(f"    地址: {result.get('address', '未知')}")

        # 显示法定代表人（多字段新增）
        if 'legal_representative' in result:
            click.echo(f"    法定代表人: {result['legal_representative']}")

        # 显示其他关键信息
        if 'company_record' in result:
            record = result['company_record']
            if '统一社会信用代码' in record:
                click.echo(f"    信用代码: {record['统一社会信用代码']}")
            if '登记状态' in record:
                click.echo(f"    登记状态: {record['登记状态']}")
            if '成立日期' in record:
                click.echo(f"    成立日期: {record['成立日期']}")
            if '经营范围' in record:
                scope = record['经营范围']
                if len(scope) > 100:
                    scope = scope[:100] + "..."
                click.echo(f"    经营范围: {scope}")

        click.echo()


def _display_results_json(results, output_file: Optional[str] = None):
    """以JSON格式显示结果"""
    json_data = json.dumps(results, ensure_ascii=False, indent=2)
    
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(json_data)
        click.echo(f"✅ 结果已保存到 {output_file}")
    else:
        click.echo(json_data)


def _export_results_csv(engine, results, output_file: Optional[str] = None):
    """导出CSV格式结果"""
    if not output_file:
        output_file = f"search_results_{int(time.time())}.csv"

    try:
        # 使用增强搜索引擎的导出功能
        if hasattr(engine, 'export_results'):
            engine.export_results(results, output_file, format='csv')
        else:
            # 备用导出方法
            import pandas as pd
            export_data = []
            for result in results:
                # 创建基础搜索信息
                row = {
                    'similarity': result['similarity'],
                    'company_name': result.get('company_name', ''),
                    'address': result.get('address', ''),
                    'legal_representative': result.get('legal_representative', ''),
                    'registration_status': result.get('registration_status', ''),
                    'establishment_date': result.get('establishment_date', ''),
                    'tax_id': result.get('tax_id', ''),
                    'phone': result.get('phone', ''),
                    'credit_code': result.get('credit_code', ''),
                    'business_scope': result.get('business_scope', ''),
                }

                # 如果有完整的原始记录，添加所有字段
                if 'company_record' in result and result['company_record']:
                    # 先添加原始记录的所有字段
                    for key, value in result['company_record'].items():
                        # 避免覆盖已有的搜索相关字段
                        if key not in row:
                            row[key] = value

                export_data.append(row)

            df = pd.DataFrame(export_data)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')

        click.echo(f"✅ 结果已导出到 {output_file}")
    except Exception as e:
        click.echo(f"❌ 导出失败: {e}", err=True)


def _export_batch_results(all_results, output_file: str):
    """导出批量搜索结果"""
    import pandas as pd
    
    # 合并所有结果
    combined_results = []
    for query_results in all_results:
        combined_results.extend(query_results)
    
    if not combined_results:
        click.echo("⚠️ 没有找到任何匹配结果")
        return
    
    # 准备导出数据
    export_data = []
    for result in combined_results:
        row = {
            'query': result['query'],
            'similarity': result['similarity'],
            'address': result['address'],
            **result['company_record']
        }
        export_data.append(row)
    
    # 导出到Excel
    df = pd.DataFrame(export_data)
    df.to_excel(output_file, index=False)


def _build_semantic_index(config_path: str, force: bool) -> bool:
    """构建语义搜索索引"""
    try:
        # 初始化增强搜索引擎
        engine = EnhancedSemanticSearchEngine(config_path)

        # 如果强制重建，先清理语义搜索相关缓存
        if force:
            cache_dir = engine.config['vector_storage']['cache_dir']
            if os.path.exists(cache_dir):
                # 只删除语义搜索相关的文件
                semantic_files = [
                    'multi_index_primary.faiss',
                    'multi_index_identifier.faiss',
                    'multi_index_status.faiss',
                    'multi_index_date.faiss',
                    'multi_vector_metadata.pkl'
                ]
                for file in semantic_files:
                    file_path = os.path.join(cache_dir, file)
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        click.echo(f"   🗑️ 删除旧文件: {file}")

        # 构建索引
        if engine.initialize():
            # 显示状态信息
            status = engine.get_system_status()
            click.echo(f"   📊 企业记录数: {status['total_records']}")
            click.echo(f"   🔢 向量维度: {status['vector_dimensions']}")
            click.echo(f"   💾 索引类型: {status['index_types']}")
            return True
        else:
            return False

    except Exception as e:
        click.echo(f"   ❌ 语义搜索索引构建错误: {e}")
        return False


def _build_tfidf_model(config_path: str, force: bool) -> bool:
    """构建TF-IDF模型"""
    try:
        from src.traditional_search_engines import TFIDFSearchEngine

        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 如果强制重建，先清理TF-IDF相关缓存
        if force:
            cache_dir = config['vector_storage']['cache_dir']
            if os.path.exists(cache_dir):
                # 只删除TF-IDF相关的文件
                tfidf_files = [
                    'tfidf_model.pkl',
                    'tfidf_vectors.pkl',
                    'tfidf_data_hash.txt'
                ]
                for file in tfidf_files:
                    file_path = os.path.join(cache_dir, file)
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        click.echo(f"   🗑️ 删除旧文件: {file}")

        # 初始化TF-IDF搜索引擎（这会触发模型构建）
        engine = TFIDFSearchEngine(config)

        # 调用初始化方法来加载数据和构建模型
        if engine.initialize():
            click.echo(f"   📚 词汇表大小: {len(engine.vocabulary)}")
            click.echo(f"   📊 文档数量: {len(engine.data)}")
            return True
        else:
            return False

    except Exception as e:
        click.echo(f"   ❌ TF-IDF模型构建错误: {e}")
        return False


def _build_exact_search(config_path: str, force: bool) -> bool:
    """初始化精确匹配搜索引擎"""
    try:
        from src.traditional_search_engines import ExactKeywordSearchEngine

        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 精确匹配不需要缓存文件，但可以测试初始化
        engine = ExactKeywordSearchEngine(config)

        # 调用初始化方法来加载数据
        if engine.initialize():
            click.echo(f"   📊 企业记录数: {len(engine.data)}")
            click.echo(f"   📋 搜索字段: {list(engine.field_weights.keys())}")
            click.echo("   ℹ️ 精确匹配无需预构建索引，实时计算")
            return True
        else:
            return False

    except Exception as e:
        click.echo(f"   ❌ 精确匹配搜索引擎初始化错误: {e}")
        return False


if __name__ == '__main__':
    cli()
