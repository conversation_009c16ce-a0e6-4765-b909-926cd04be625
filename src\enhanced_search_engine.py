#!/usr/bin/env python3
"""
增强的语义搜索引擎
支持多字段向量化和分层搜索策略
"""

import yaml
import numpy as np
import time
from typing import Dict, List, Any, Optional, Tuple
import os

from .data_processor import DataProcessor
from .multi_vectorizer import MultiFieldVectorizer
from .multi_index_builder import MultiVectorIndexBuilder
from .utils import setup_logger

logger = setup_logger(__name__)

class EnhancedSemanticSearchEngine:
    """
    增强的语义搜索引擎
    支持多字段向量化和多种搜索策略
    """
    
    def __init__(self, config_path: str):
        """
        初始化增强搜索引擎
        
        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 检查是否启用多字段向量化
        self.multi_field_enabled = self.config.get('data', {}).get('multi_field', {}).get('enabled', False)

        if not self.multi_field_enabled:
            raise RuntimeError("Multi-field vectorization must be enabled in config.yaml")

        logger.info("Enhanced search engine initialized with multi-field vectorization")
        self._init_multi_field_components()
        
        # 通用组件
        self.data_processor = DataProcessor(self.config)
        self.is_initialized = False
        
        # 搜索配置
        search_config = self.config.get('search', {})
        self.similarity_threshold = search_config.get('similarity_threshold', 0.85)
        self.max_results = search_config.get('max_results', 10)
    
    def _init_multi_field_components(self):
        """初始化多字段组件"""
        self.multi_vectorizer = MultiFieldVectorizer(self.config)
        self.multi_index_builder = MultiVectorIndexBuilder(self.config)
        self.search_mode = 'multi_field'
    
    def initialize(self) -> bool:
        """
        初始化搜索引擎
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("Initializing enhanced search engine...")
            return self._initialize_multi_field()

        except Exception as e:
            logger.error(f"Failed to initialize enhanced search engine: {e}")
            return False
    
    def _initialize_multi_field(self) -> bool:
        """初始化多字段模式"""
        # 1. 加载模型
        self.multi_vectorizer.load_model()
        
        # 2. 尝试加载已有索引
        if self.multi_index_builder.load_indexes():
            logger.info("Loaded existing multi-field indexes")
            # 即使加载了索引，也需要加载数据以支持搜索结果处理
            addresses, df = self.data_processor.process_data()
            self.is_initialized = True
            return True
        
        # 3. 如果没有索引，则构建新索引
        logger.info("Building new multi-field indexes...")
        
        # 处理数据
        addresses, df = self.data_processor.process_data()
        
        # 获取多字段文本
        multi_field_texts = self.data_processor.get_multi_field_texts()
        
        # 向量化
        vectorized_records = self.multi_vectorizer.vectorize_multi_field_data(multi_field_texts)
        
        # 构建索引
        build_result = self.multi_index_builder.build_indexes(vectorized_records)
        
        logger.info(f"Multi-field indexes built: {build_result}")
        self.is_initialized = True
        return True
    
    def search(
        self, 
        query: str, 
        top_k: Optional[int] = None,
        threshold: Optional[float] = None,
        search_strategy: str = 'auto',
        field_hints: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        执行搜索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            threshold: 相似度阈值
            search_strategy: 搜索策略
                - 'auto': 自动选择最佳策略
                - 'primary_only': 仅使用主向量
                - 'weighted_fusion': 加权融合
                - 'cascade': 级联搜索
            field_hints: 字段提示
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        if not self.is_initialized:
            raise RuntimeError("Search engine not initialized")
        
        # 参数处理
        top_k = top_k or self.max_results
        threshold = float(threshold) if threshold is not None else self.similarity_threshold
        
        return self._search_multi_field(query, top_k, threshold, search_strategy, field_hints)
    
    def _search_multi_field(
        self,
        query: str,
        top_k: int,
        threshold: float,
        search_strategy: str,
        field_hints: Optional[List[str]]
    ) -> List[Dict[str, Any]]:
        """多字段搜索实现"""
        logger.info(f"Searching for: '{query}' (top_k={top_k}, threshold={threshold})")
        start_time = time.time()

        try:
            # 1. 向量化查询
            logger.info("Encoding query to vectors")
            encode_start = time.time()
            query_vectors = self.multi_vectorizer.encode_query_multi_field(query, field_hints)
            encode_time = time.time() - encode_start
            logger.info(f"Encoding completed in {encode_time:.2f}s")

            # 记录向量形状
            if 'primary' in query_vectors:
                vector_shape = query_vectors['primary'].shape
                logger.info(f"Vector shape: {vector_shape}")

            # 2. 自动选择搜索策略
            if search_strategy == 'auto':
                search_strategy = self._auto_select_strategy(query, field_hints)

            # 3. 执行搜索
            scores, indices = self.multi_index_builder.search_multi_vector(
                query_vectors,
                k=min(top_k * 2, len(self.multi_index_builder.vector_records)),
                search_strategy=search_strategy
            )

            # 4. 处理搜索结果
            results = self._process_multi_field_results(
                query, scores, indices, threshold, top_k
            )

            elapsed_time = time.time() - start_time
            logger.info(f"Search completed in {elapsed_time:.3f}s, found {len(results)} results")

            return results

        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise
    
    def _auto_select_strategy(self, query: str, field_hints: Optional[List[str]]) -> str:
        """自动选择搜索策略"""
        # 简单的策略选择逻辑
        if field_hints and len(field_hints) > 1:
            return 'weighted_fusion'
        elif len(query.split()) > 5:
            return 'cascade'
        else:
            return 'primary_only'
    
    def _process_multi_field_results(
        self,
        query: str,
        scores: np.ndarray,
        indices: np.ndarray,
        threshold: float,
        top_k: int
    ) -> List[Dict[str, Any]]:
        """处理多字段搜索结果"""
        results = []
        
        for score, idx in zip(scores, indices):
            # 确保score和idx是标量值
            score = float(score) if hasattr(score, 'item') else float(score)
            idx = int(idx) if hasattr(idx, 'item') else int(idx)
            
            # 检查索引有效性
            if idx < 0 or idx >= len(self.multi_index_builder.vector_records):
                continue
            
            # 应用相似度阈值
            if score < threshold:
                continue
            
            # 获取向量化记录
            vector_record = self.multi_index_builder.vector_records[idx]
            
            # 获取原始数据记录
            original_record = self.data_processor.get_record_by_index(vector_record.record_index)
            
            # 构建结果
            result = {
                'similarity': score,
                'index': vector_record.record_index,
                'company_name': original_record.get('公司名称', ''),
                'address': original_record.get('最新年报地址', ''),
                'legal_representative': original_record.get('法定代表人', ''),
                'registration_status': original_record.get('登记状态', ''),
                'establishment_date': original_record.get('成立日期', ''),
                'tax_id': original_record.get('纳税人识别号', ''),
                'phone': original_record.get('有效手机号', ''),
                'credit_code': original_record.get('统一社会信用代码', ''),
                'business_scope': original_record.get('经营范围', ''),
                'city': original_record.get('所属城市', ''),  # 添加城市信息
                'metadata': {
                    'primary_text': vector_record.metadata.get('primary_text', ''),
                    'field_weights': vector_record.metadata.get('field_weights', {})
                },
                # 添加完整的原始记录，用于导出
                'company_record': original_record
            }
            
            results.append(result)
        
        # 按相似度排序并限制结果数量
        results.sort(key=lambda x: x['similarity'], reverse=True)
        return results[:top_k]

    def export_results(self, results: List[Dict], output_file: str, format: str = 'csv'):
        """
        导出搜索结果到文件，包含完整的原始字段信息

        Args:
            results: 搜索结果列表
            output_file: 输出文件路径
            format: 导出格式 ('csv' 或 'excel')
        """
        import pandas as pd

        if not results:
            logger.warning("No results to export")
            return

        export_data = []
        for result in results:
            # 创建包含搜索信息的基础行
            row = {
                'similarity': result['similarity'],
                'index': result.get('index', ''),
                'company_name': result.get('company_name', ''),
                'address': result.get('address', ''),
                'legal_representative': result.get('legal_representative', ''),
                'registration_status': result.get('registration_status', ''),
                'establishment_date': result.get('establishment_date', ''),
                'tax_id': result.get('tax_id', ''),
                'phone': result.get('phone', ''),
                'credit_code': result.get('credit_code', ''),
                'business_scope': result.get('business_scope', ''),
            }

            # 添加完整的原始记录字段
            if 'company_record' in result and result['company_record']:
                for key, value in result['company_record'].items():
                    # 使用原始字段名，避免覆盖搜索相关字段
                    if key not in row:
                        row[key] = value

            export_data.append(row)

        # 创建DataFrame并导出
        df = pd.DataFrame(export_data)

        if format.lower() == 'csv':
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
        elif format.lower() in ['excel', 'xlsx']:
            df.to_excel(output_file, index=False)
        else:
            raise ValueError(f"Unsupported export format: {format}")

        logger.info(f"Results exported to {output_file} with {len(export_data)} records")

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        if self.search_mode == 'single_field':
            return self.fallback_engine.get_system_status()
        
        if not self.is_initialized:
            return {"status": "not_initialized", "mode": "multi_field"}
        
        # 多字段模式状态
        vector_dimensions = self.multi_vectorizer.get_vector_dimensions()
        
        status = {
            "status": "initialized",
            "mode": "multi_field",
            "total_records": len(self.multi_index_builder.vector_records),
            "index_types": list(self.multi_index_builder.indexes.keys()),
            "vector_dimensions": vector_dimensions,
            "config": {
                "primary_fields": self.config['data']['multi_field']['primary_fields'],
                "auxiliary_fields": self.config['data']['multi_field']['auxiliary_fields'],
                "similarity_threshold": self.similarity_threshold,
                "max_results": self.max_results
            }
        }
        
        return status
    

