#!/usr/bin/env python3
"""
多字段向量化器
实现分层向量化架构，支持主向量和辅助向量的独立处理
"""

import numpy as np
import torch
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import time
from tqdm import tqdm

from .vectorizer import BGEVectorizer
from .utils import setup_logger

logger = setup_logger(__name__)

@dataclass
class VectorizedRecord:
    """向量化记录数据结构"""
    primary_vector: np.ndarray      # 主向量（用于快速检索）
    auxiliary_vectors: Dict[str, np.ndarray]  # 辅助向量字典
    metadata: Dict[str, Any]        # 元数据
    record_index: int               # 原始记录索引

class MultiFieldVectorizer:
    """
    多字段向量化器
    实现分层向量化架构
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化多字段向量化器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.multi_field_config = config.get('data', {}).get('multi_field', {})
        
        # 检查是否启用多字段向量化
        self.enabled = self.multi_field_config.get('enabled', False)
        
        if not self.enabled:
            logger.info("Multi-field vectorization is disabled")
            return
            
        # 初始化基础向量化器
        self.base_vectorizer = BGEVectorizer(config)
        
        # 向量化配置
        self.primary_fields = self.multi_field_config.get('primary_fields', {})
        self.auxiliary_fields = self.multi_field_config.get('auxiliary_fields', {})
        
        # 向量缓存
        self.vector_cache = {}
        
        logger.info(f"Multi-field vectorizer initialized")
        logger.info(f"Primary fields: {list(self.primary_fields.keys())}")
        logger.info(f"Auxiliary fields: {list(self.auxiliary_fields.keys())}")
    
    def load_model(self) -> None:
        """加载向量化模型"""
        if not self.enabled:
            return
            
        self.base_vectorizer.load_model()
        logger.info("Multi-field vectorizer model loaded")
    
    def vectorize_multi_field_data(
        self, 
        multi_field_texts: Dict[str, List[str]],
        show_progress: bool = True
    ) -> List[VectorizedRecord]:
        """
        对多字段数据进行向量化
        
        Args:
            multi_field_texts: 多字段文本数据
            show_progress: 是否显示进度
            
        Returns:
            List[VectorizedRecord]: 向量化记录列表
        """
        if not self.enabled:
            raise RuntimeError("Multi-field vectorization is not enabled")
            
        logger.info("Starting multi-field vectorization...")
        start_time = time.time()
        
        # 获取记录数量
        record_count = len(next(iter(multi_field_texts.values())))
        
        # 1. 创建主向量文本
        primary_texts = self._create_primary_texts(multi_field_texts, record_count)
        
        # 2. 向量化主文本
        logger.info("Vectorizing primary texts...")
        primary_vectors = self.base_vectorizer.encode_texts(primary_texts, show_progress)
        
        # 3. 向量化辅助字段
        auxiliary_vectors_dict = {}
        for field_type in self.auxiliary_fields.keys():
            if field_type in multi_field_texts:
                logger.info(f"Vectorizing auxiliary field: {field_type}")
                aux_vectors = self.base_vectorizer.encode_texts(
                    multi_field_texts[field_type],
                    show_progress=True
                )
                auxiliary_vectors_dict[field_type] = aux_vectors
        
        # 4. 创建向量化记录
        logger.info("Creating vectorized records...")
        vectorized_records = []
        for i in tqdm(range(record_count), desc="创建向量记录"):
            aux_vectors = {}
            for field_type, vectors in auxiliary_vectors_dict.items():
                aux_vectors[field_type] = vectors[i]
            
            record = VectorizedRecord(
                primary_vector=primary_vectors[i],
                auxiliary_vectors=aux_vectors,
                metadata={
                    'primary_text': primary_texts[i],
                    'field_weights': self.primary_fields
                },
                record_index=i
            )
            vectorized_records.append(record)
        
        elapsed_time = time.time() - start_time
        logger.info(f"Multi-field vectorization completed in {elapsed_time:.2f}s")
        logger.info(f"Generated {len(vectorized_records)} vectorized records")
        
        return vectorized_records
    
    def _create_primary_texts(
        self,
        multi_field_texts: Dict[str, List[str]],
        record_count: int
    ) -> List[str]:
        """
        创建主向量文本（加权组合）

        Args:
            multi_field_texts: 多字段文本数据
            record_count: 记录数量

        Returns:
            List[str]: 主向量文本列表
        """
        primary_texts = []

        for i in range(record_count):
            weighted_parts = []

            # 按语义权重排序字段，权重高的放在前面
            sorted_fields = sorted(self.primary_fields.items(),
                                 key=lambda x: x[1].get('semantic_weight', 0.0), reverse=True)

            for field, weight_config in sorted_fields:
                if field in multi_field_texts:
                    text = multi_field_texts[field][i]
                    if text and text.strip():
                        # 获取语义权重
                        semantic_weight = weight_config.get('semantic_weight', 0.0)
                        # 改进权重实现：更细粒度的权重映射
                        if semantic_weight >= 0.6:
                            repeat_count = 3  # 超高权重字段重复3次
                        elif semantic_weight >= 0.4:
                            repeat_count = 2  # 高权重字段重复2次
                        elif semantic_weight >= 0.2:
                            repeat_count = 1  # 中等权重字段重复1次
                        else:
                            repeat_count = 1  # 低权重字段重复1次

                        weighted_parts.extend([text] * repeat_count)

            primary_text = ' '.join(weighted_parts) if weighted_parts else '空记录'
            primary_texts.append(primary_text)

        return primary_texts
    
    def encode_query_multi_field(self, query: str, field_hints: Optional[List[str]] = None) -> Dict[str, np.ndarray]:
        """
        对查询进行多字段向量化
        
        Args:
            query: 查询文本
            field_hints: 字段提示（指定查询主要针对哪些字段）
            
        Returns:
            Dict[str, np.ndarray]: 包含主向量和相关辅助向量的字典
        """
        if not self.enabled:
            # 回退到单向量模式
            vector = self.base_vectorizer.encode_single_text(query)
            return {'primary': vector}
        
        result = {}
        
        # 主向量（总是生成）
        result['primary'] = self.base_vectorizer.encode_single_text(query)
        
        # 根据字段提示生成辅助向量
        if field_hints:
            for field_type in field_hints:
                if field_type in self.auxiliary_fields:
                    # 为特定字段类型优化查询文本
                    optimized_query = self._optimize_query_for_field(query, field_type)
                    result[field_type] = self.base_vectorizer.encode_single_text(optimized_query)
        
        return result
    
    def _optimize_query_for_field(self, query: str, field_type: str) -> str:
        """
        为特定字段类型优化查询文本
        
        Args:
            query: 原始查询
            field_type: 字段类型
            
        Returns:
            str: 优化后的查询文本
        """
        # 简单的查询优化策略
        if field_type == 'status':
            # 状态字段：添加状态相关关键词
            return f"{query} 状态 登记"
        elif field_type == 'date':
            # 日期字段：添加时间相关关键词
            return f"{query} 成立 日期 时间"
        elif field_type == 'identifier':
            # 标识符字段：保持原样
            return query
        else:
            return query
    
    def get_vector_dimensions(self) -> Dict[str, int]:
        """
        获取各类向量的维度信息
        
        Returns:
            Dict[str, int]: 向量维度字典
        """
        if not self.enabled:
            return {'primary': self.base_vectorizer.get_dimension()}
        
        # 所有向量都使用相同的维度（BGE-M3模型）
        dim = self.base_vectorizer.get_embedding_dimension()
        result = {'primary': dim}
        
        for field_type in self.auxiliary_fields.keys():
            result[field_type] = dim
        
        return result
