2025-07-31 20:24:28 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 20:24:28 | INFO | Index loaded successfully in 0.02s
2025-07-31 20:24:28 | INFO | Index contains 5344 vectors
2025-07-31 20:24:28 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 20:24:32 | INFO | Model loaded successfully in 3.61s
2025-07-31 20:24:32 | INFO | Device: cuda
2025-07-31 20:24:32 | INFO | Max sequence length: 512
2025-07-31 20:24:32 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:24:34 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:24:34 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:24:34 | INFO | Address field validation:
2025-07-31 20:24:34 | INFO |   - Total records: 5334
2025-07-31 20:24:34 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:24:34 | INFO | Cleaning address data...
2025-07-31 20:24:34 | INFO | Address cleaning completed:
2025-07-31 20:24:34 | INFO |   - Valid addresses: 5334
2025-07-31 20:24:34 | INFO |   - Empty addresses: 0
2025-07-31 20:24:34 | INFO | Data processing completed successfully
2025-07-31 20:24:34 | INFO | Searching for: '杭州' (top_k=5, threshold=0.85)
2025-07-31 20:24:34 | INFO | Encoding 1 texts to vectors
2025-07-31 20:24:35 | INFO | Encoding completed in 1.43s
2025-07-31 20:24:35 | INFO | Vector shape: (1, 1024)
2025-07-31 20:24:35 | INFO | Search completed in 1.440s, found 0 results
2025-07-31 20:25:28 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 20:25:28 | INFO | Index loaded successfully in 0.01s
2025-07-31 20:25:28 | INFO | Index contains 5344 vectors
2025-07-31 20:25:28 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 20:25:30 | INFO | Model loaded successfully in 2.79s
2025-07-31 20:25:30 | INFO | Device: cuda
2025-07-31 20:25:30 | INFO | Max sequence length: 512
2025-07-31 20:25:30 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:25:33 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:25:33 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:25:33 | INFO | Address field validation:
2025-07-31 20:25:33 | INFO |   - Total records: 5334
2025-07-31 20:25:33 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:25:33 | INFO | Cleaning address data...
2025-07-31 20:25:33 | INFO | Address cleaning completed:
2025-07-31 20:25:33 | INFO |   - Valid addresses: 5334
2025-07-31 20:25:33 | INFO |   - Empty addresses: 0
2025-07-31 20:25:33 | INFO | Data processing completed successfully
2025-07-31 20:25:33 | INFO | Searching for: '杭州' (top_k=5, threshold=0.7)
2025-07-31 20:25:33 | INFO | Encoding 1 texts to vectors
2025-07-31 20:25:34 | INFO | Encoding completed in 1.01s
2025-07-31 20:25:34 | INFO | Vector shape: (1, 1024)
2025-07-31 20:25:34 | INFO | Search completed in 1.015s, found 3 results
2025-07-31 20:34:04 | INFO | Initializing semantic search engine...
2025-07-31 20:34:04 | INFO | Step 1: Processing data...
2025-07-31 20:34:04 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:34:06 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:34:06 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:34:06 | INFO | Address field validation:
2025-07-31 20:34:06 | INFO |   - Total records: 5334
2025-07-31 20:34:06 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:34:06 | INFO | Cleaning address data...
2025-07-31 20:34:06 | INFO | Address cleaning completed:
2025-07-31 20:34:06 | INFO |   - Valid addresses: 5334
2025-07-31 20:34:06 | INFO |   - Empty addresses: 0
2025-07-31 20:34:06 | INFO | Data processing completed successfully
2025-07-31 20:34:06 | INFO | Step 2: Vectorizing addresses...
2025-07-31 20:34:06 | INFO | Vectors loaded from ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-07-31 20:34:06 | INFO | Using cached vectors from ./cache\company_addresses_company_vectors.npy
2025-07-31 20:34:06 | INFO | Vector validation passed
2025-07-31 20:34:06 | INFO | Step 3: Building search index...
2025-07-31 20:34:06 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 20:34:06 | INFO | Index loaded successfully in 0.01s
2025-07-31 20:34:06 | INFO | Index contains 5344 vectors
2025-07-31 20:34:06 | INFO | Vector count mismatch, rebuilding index
2025-07-31 20:34:06 | INFO | Building new index...
2025-07-31 20:34:06 | INFO | Building index for 5334 vectors with dimension 1024
2025-07-31 20:34:06 | INFO | Adding vectors to index...
2025-07-31 20:34:06 | INFO | Index built successfully in 0.01s
2025-07-31 20:34:06 | INFO | Index contains 10678 vectors
2025-07-31 20:34:06 | INFO | Saving index to ./cache\company_index.faiss
2025-07-31 20:34:06 | INFO | Index saved successfully in 0.03s
2025-07-31 20:34:06 | INFO | Index file size: 41.71 MB
2025-07-31 20:34:06 | INFO | Metadata saved to ./cache\company_metadata.pkl
2025-07-31 20:34:06 | INFO | Search engine initialized successfully in 2.51s
2025-07-31 20:34:06 | INFO | Ready to search 5334 company records
2025-07-31 20:40:30 | INFO | Initializing semantic search engine...
2025-07-31 20:40:30 | INFO | Step 1: Processing data...
2025-07-31 20:40:30 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:40:33 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:40:33 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:40:33 | INFO | Address field validation:
2025-07-31 20:40:33 | INFO |   - Total records: 5334
2025-07-31 20:40:33 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:40:33 | INFO | Cleaning address data...
2025-07-31 20:40:33 | INFO | Address cleaning completed:
2025-07-31 20:40:33 | INFO |   - Valid addresses: 5334
2025-07-31 20:40:33 | INFO |   - Empty addresses: 0
2025-07-31 20:40:33 | INFO | Data processing completed successfully
2025-07-31 20:40:33 | INFO | Step 2: Vectorizing addresses...
2025-07-31 20:40:33 | INFO | Generating new vectors...
2025-07-31 20:40:33 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 20:40:35 | INFO | Model loaded successfully in 2.86s
2025-07-31 20:40:35 | INFO | Device: cuda
2025-07-31 20:40:35 | INFO | Max sequence length: 512
2025-07-31 20:40:35 | INFO | Encoding 5334 texts to vectors
2025-07-31 20:40:55 | INFO | Encoding completed in 19.51s
2025-07-31 20:40:55 | INFO | Vector shape: (5334, 1024)
2025-07-31 20:40:55 | INFO | Vectors saved to ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-07-31 20:40:55 | INFO | Vector validation passed
2025-07-31 20:40:55 | INFO | Step 3: Building search index...
2025-07-31 20:40:55 | INFO | Building new index...
2025-07-31 20:40:55 | INFO | Building index for 5334 vectors with dimension 1024
2025-07-31 20:40:55 | INFO | Creating HNSW index with dimension 1024
2025-07-31 20:40:55 | INFO | HNSW parameters:
2025-07-31 20:40:55 | INFO |   - M: 32
2025-07-31 20:40:55 | INFO |   - efConstruction: 200
2025-07-31 20:40:55 | INFO |   - efSearch: 100
2025-07-31 20:40:55 | INFO | Adding vectors to index...
2025-07-31 20:45:19 | INFO | Initializing semantic search engine...
2025-07-31 20:45:19 | INFO | Step 1: Processing data...
2025-07-31 20:45:19 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:45:22 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:45:22 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:45:22 | INFO | Address field validation:
2025-07-31 20:45:22 | INFO |   - Total records: 5334
2025-07-31 20:45:22 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:45:22 | INFO | Cleaning address data...
2025-07-31 20:45:22 | INFO | Address cleaning completed:
2025-07-31 20:45:22 | INFO |   - Valid addresses: 5334
2025-07-31 20:45:22 | INFO |   - Empty addresses: 0
2025-07-31 20:45:22 | INFO | Data processing completed successfully
2025-07-31 20:45:22 | INFO | Step 2: Vectorizing addresses...
2025-07-31 20:45:22 | INFO | Generating new vectors...
2025-07-31 20:45:22 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 20:45:24 | INFO | Model loaded successfully in 2.51s
2025-07-31 20:45:24 | INFO | Device: cuda
2025-07-31 20:45:24 | INFO | Max sequence length: 512
2025-07-31 20:45:24 | INFO | Encoding 5334 texts to vectors
2025-07-31 20:45:43 | INFO | Encoding completed in 18.58s
2025-07-31 20:45:43 | INFO | Vector shape: (5334, 1024)
2025-07-31 20:45:43 | INFO | Vectors saved to ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-07-31 20:45:43 | INFO | Vector validation passed
2025-07-31 20:45:43 | INFO | Step 3: Building search index...
2025-07-31 20:45:43 | INFO | Building new index...
2025-07-31 20:45:43 | INFO | Building index for 5334 vectors with dimension 1024
2025-07-31 20:45:43 | INFO | Creating HNSW index with dimension 1024
2025-07-31 20:45:43 | INFO | HNSW parameters:
2025-07-31 20:45:43 | INFO |   - M: 16
2025-07-31 20:45:43 | INFO |   - efConstruction: 200
2025-07-31 20:45:43 | INFO |   - efSearch: 100
2025-07-31 20:45:43 | INFO | Adding vectors to index...
2025-07-31 20:46:12 | INFO | Initializing semantic search engine...
2025-07-31 20:46:12 | INFO | Step 1: Processing data...
2025-07-31 20:46:12 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:46:14 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:46:14 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:46:14 | INFO | Address field validation:
2025-07-31 20:46:14 | INFO |   - Total records: 5334
2025-07-31 20:46:14 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:46:14 | INFO | Cleaning address data...
2025-07-31 20:46:14 | INFO | Address cleaning completed:
2025-07-31 20:46:14 | INFO |   - Valid addresses: 5334
2025-07-31 20:46:14 | INFO |   - Empty addresses: 0
2025-07-31 20:46:14 | INFO | Data processing completed successfully
2025-07-31 20:46:14 | INFO | Step 2: Vectorizing addresses...
2025-07-31 20:46:14 | INFO | Vectors loaded from ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-07-31 20:46:14 | INFO | Using cached vectors from ./cache\company_addresses_company_vectors.npy
2025-07-31 20:46:14 | INFO | Vector validation passed
2025-07-31 20:46:14 | INFO | Step 3: Building search index...
2025-07-31 20:46:14 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 20:46:14 | INFO | Index loaded successfully in 0.04s
2025-07-31 20:46:14 | INFO | Index contains 10678 vectors
2025-07-31 20:46:14 | INFO | Vector count mismatch, rebuilding index
2025-07-31 20:46:14 | INFO | Building new index...
2025-07-31 20:46:14 | INFO | Building index for 5334 vectors with dimension 1024
2025-07-31 20:46:14 | INFO | Adding vectors to index...
2025-07-31 20:46:14 | INFO | Index built successfully in 0.01s
2025-07-31 20:46:14 | INFO | Index contains 16012 vectors
2025-07-31 20:46:14 | INFO | Saving index to ./cache\company_index.faiss
2025-07-31 20:46:14 | INFO | Index saved successfully in 0.04s
2025-07-31 20:46:14 | INFO | Index file size: 62.55 MB
2025-07-31 20:46:14 | INFO | Metadata saved to ./cache\company_metadata.pkl
2025-07-31 20:46:14 | INFO | Search engine initialized successfully in 2.44s
2025-07-31 20:46:14 | INFO | Ready to search 5334 company records
2025-07-31 20:46:33 | INFO | Initializing semantic search engine...
2025-07-31 20:46:33 | INFO | Step 1: Processing data...
2025-07-31 20:46:33 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:46:35 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:46:35 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:46:35 | INFO | Address field validation:
2025-07-31 20:46:35 | INFO |   - Total records: 5334
2025-07-31 20:46:35 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:46:35 | INFO | Cleaning address data...
2025-07-31 20:46:35 | INFO | Address cleaning completed:
2025-07-31 20:46:35 | INFO |   - Valid addresses: 5334
2025-07-31 20:46:35 | INFO |   - Empty addresses: 0
2025-07-31 20:46:35 | INFO | Data processing completed successfully
2025-07-31 20:46:35 | INFO | Step 2: Vectorizing addresses...
2025-07-31 20:46:35 | INFO | Generating new vectors...
2025-07-31 20:46:35 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 20:46:38 | INFO | Model loaded successfully in 2.49s
2025-07-31 20:46:38 | INFO | Device: cuda
2025-07-31 20:46:38 | INFO | Max sequence length: 512
2025-07-31 20:46:38 | INFO | Encoding 5334 texts to vectors
2025-07-31 20:46:56 | INFO | Encoding completed in 18.51s
2025-07-31 20:46:56 | INFO | Vector shape: (5334, 1024)
2025-07-31 20:46:56 | INFO | Vectors saved to ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-07-31 20:46:56 | INFO | Vector validation passed
2025-07-31 20:46:56 | INFO | Step 3: Building search index...
2025-07-31 20:46:56 | INFO | Building new index...
2025-07-31 20:46:56 | INFO | Building index for 5334 vectors with dimension 1024
2025-07-31 20:46:56 | INFO | Creating HNSW index with dimension 1024
2025-07-31 20:46:56 | INFO | HNSW parameters:
2025-07-31 20:46:56 | INFO |   - M: 16
2025-07-31 20:46:56 | INFO |   - efConstruction: 200
2025-07-31 20:46:56 | INFO |   - efSearch: 100
2025-07-31 20:46:56 | INFO | Adding vectors to index...
2025-07-31 20:48:55 | INFO | Initializing semantic search engine...
2025-07-31 20:48:55 | INFO | Step 1: Processing data...
2025-07-31 20:48:55 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:48:57 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:48:57 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:48:57 | INFO | Address field validation:
2025-07-31 20:48:57 | INFO |   - Total records: 5334
2025-07-31 20:48:57 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:48:57 | INFO | Cleaning address data...
2025-07-31 20:48:57 | INFO | Address cleaning completed:
2025-07-31 20:48:57 | INFO |   - Valid addresses: 5334
2025-07-31 20:48:57 | INFO |   - Empty addresses: 0
2025-07-31 20:48:57 | INFO | Data processing completed successfully
2025-07-31 20:48:57 | INFO | Step 2: Vectorizing addresses...
2025-07-31 20:48:57 | INFO | Generating new vectors...
2025-07-31 20:48:57 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 20:49:00 | INFO | Model loaded successfully in 2.67s
2025-07-31 20:49:00 | INFO | Device: cuda
2025-07-31 20:49:00 | INFO | Max sequence length: 512
2025-07-31 20:49:00 | INFO | Encoding 5334 texts to vectors
2025-07-31 20:49:19 | INFO | Encoding completed in 19.09s
2025-07-31 20:49:19 | INFO | Vector shape: (5334, 1024)
2025-07-31 20:49:19 | INFO | Vectors saved to ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-07-31 20:49:19 | INFO | Vector validation passed
2025-07-31 20:49:19 | INFO | Step 3: Building search index...
2025-07-31 20:49:19 | INFO | Building new index...
2025-07-31 20:49:19 | INFO | Building index for 5334 vectors with dimension 1024
2025-07-31 20:49:19 | INFO | Creating FLAT index with dimension 1024
2025-07-31 20:49:19 | INFO | Adding vectors to index...
2025-07-31 20:49:19 | INFO | Index built successfully in 0.01s
2025-07-31 20:49:19 | INFO | Index contains 5334 vectors
2025-07-31 20:49:19 | INFO | Saving index to ./cache\company_index.faiss
2025-07-31 20:49:19 | INFO | Index saved successfully in 0.01s
2025-07-31 20:49:19 | INFO | Index file size: 20.84 MB
2025-07-31 20:49:19 | INFO | Metadata saved to ./cache\company_metadata.pkl
2025-07-31 20:49:19 | INFO | Search engine initialized successfully in 24.30s
2025-07-31 20:49:19 | INFO | Ready to search 5334 company records
2025-07-31 20:54:27 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 20:54:27 | INFO | Index loaded successfully in 0.02s
2025-07-31 20:54:27 | INFO | Index contains 5334 vectors
2025-07-31 20:54:27 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 20:54:29 | INFO | Model loaded successfully in 2.67s
2025-07-31 20:54:29 | INFO | Device: cuda
2025-07-31 20:54:29 | INFO | Max sequence length: 512
2025-07-31 20:54:29 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:54:32 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:54:32 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:54:32 | INFO | Address field validation:
2025-07-31 20:54:32 | INFO |   - Total records: 5334
2025-07-31 20:54:32 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:54:32 | INFO | Cleaning address data...
2025-07-31 20:54:32 | INFO | Address cleaning completed:
2025-07-31 20:54:32 | INFO |   - Valid addresses: 5334
2025-07-31 20:54:32 | INFO |   - Empty addresses: 0
2025-07-31 20:54:32 | INFO | Data processing completed successfully
2025-07-31 20:54:32 | INFO | Searching for: '杭州' (top_k=10, threshold=0.85)
2025-07-31 20:54:32 | INFO | Encoding 1 texts to vectors
2025-07-31 20:54:33 | INFO | Encoding completed in 0.97s
2025-07-31 20:54:33 | INFO | Vector shape: (1, 1024)
2025-07-31 20:54:33 | INFO | Search completed in 0.981s, found 0 results
2025-07-31 20:55:01 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 20:55:01 | INFO | Index loaded successfully in 0.01s
2025-07-31 20:55:01 | INFO | Index contains 5334 vectors
2025-07-31 20:55:01 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 20:55:04 | INFO | Model loaded successfully in 2.44s
2025-07-31 20:55:04 | INFO | Device: cuda
2025-07-31 20:55:04 | INFO | Max sequence length: 512
2025-07-31 20:55:04 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:55:06 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:55:06 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:55:06 | INFO | Address field validation:
2025-07-31 20:55:06 | INFO |   - Total records: 5334
2025-07-31 20:55:06 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:55:06 | INFO | Cleaning address data...
2025-07-31 20:55:06 | INFO | Address cleaning completed:
2025-07-31 20:55:06 | INFO |   - Valid addresses: 5334
2025-07-31 20:55:06 | INFO |   - Empty addresses: 0
2025-07-31 20:55:06 | INFO | Data processing completed successfully
2025-07-31 20:55:06 | INFO | Searching for: '杭州' (top_k=5, threshold=0.7)
2025-07-31 20:55:06 | INFO | Encoding 1 texts to vectors
2025-07-31 20:55:07 | INFO | Encoding completed in 0.87s
2025-07-31 20:55:07 | INFO | Vector shape: (1, 1024)
2025-07-31 20:55:07 | INFO | Search completed in 0.875s, found 3 results
2025-07-31 20:58:54 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 20:58:54 | INFO | Index loaded successfully in 0.01s
2025-07-31 20:58:54 | INFO | Index contains 5334 vectors
2025-07-31 20:58:54 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 20:58:57 | INFO | Model loaded successfully in 2.64s
2025-07-31 20:58:57 | INFO | Device: cuda
2025-07-31 20:58:57 | INFO | Max sequence length: 512
2025-07-31 20:58:57 | INFO | Loading data from ./data/process.xlsx
2025-07-31 20:58:59 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 20:58:59 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 20:58:59 | INFO | Address field validation:
2025-07-31 20:58:59 | INFO |   - Total records: 5334
2025-07-31 20:58:59 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 20:58:59 | INFO | Cleaning address data...
2025-07-31 20:58:59 | INFO | Address cleaning completed:
2025-07-31 20:58:59 | INFO |   - Valid addresses: 5334
2025-07-31 20:58:59 | INFO |   - Empty addresses: 0
2025-07-31 20:58:59 | INFO | Data processing completed successfully
2025-07-31 20:58:59 | INFO | Searching for: '杭州' (top_k=10, threshold=0.7)
2025-07-31 20:58:59 | INFO | Encoding 1 texts to vectors
2025-07-31 20:59:00 | INFO | Encoding completed in 0.91s
2025-07-31 20:59:00 | INFO | Vector shape: (1, 1024)
2025-07-31 20:59:00 | INFO | Search completed in 0.921s, found 3 results
2025-07-31 21:07:59 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 21:07:59 | INFO | Index loaded successfully in 0.01s
2025-07-31 21:07:59 | INFO | Index contains 5334 vectors
2025-07-31 21:07:59 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 21:08:02 | INFO | Model loaded successfully in 2.51s
2025-07-31 21:08:02 | INFO | Device: cuda
2025-07-31 21:08:02 | INFO | Max sequence length: 512
2025-07-31 21:08:02 | INFO | Loading data from ./data/process.xlsx
2025-07-31 21:08:04 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 21:08:04 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 21:08:04 | INFO | Address field validation:
2025-07-31 21:08:04 | INFO |   - Total records: 5334
2025-07-31 21:08:04 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 21:08:04 | INFO | Cleaning address data...
2025-07-31 21:08:04 | INFO | Address cleaning completed:
2025-07-31 21:08:04 | INFO |   - Valid addresses: 5334
2025-07-31 21:08:04 | INFO |   - Empty addresses: 0
2025-07-31 21:08:04 | INFO | Data processing completed successfully
2025-07-31 21:08:04 | INFO | Searching for: '宁波' (top_k=10, threshold=0.7)
2025-07-31 21:08:04 | INFO | Encoding 1 texts to vectors
2025-07-31 21:08:05 | INFO | Encoding completed in 0.91s
2025-07-31 21:08:05 | INFO | Vector shape: (1, 1024)
2025-07-31 21:08:05 | INFO | Search completed in 0.918s, found 0 results
2025-07-31 21:09:32 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 21:09:32 | INFO | Index loaded successfully in 0.01s
2025-07-31 21:09:32 | INFO | Index contains 5334 vectors
2025-07-31 21:09:32 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 21:09:34 | INFO | Model loaded successfully in 2.47s
2025-07-31 21:09:34 | INFO | Device: cuda
2025-07-31 21:09:34 | INFO | Max sequence length: 512
2025-07-31 21:09:34 | INFO | Loading data from ./data/process.xlsx
2025-07-31 21:09:37 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 21:09:37 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 21:09:37 | INFO | Address field validation:
2025-07-31 21:09:37 | INFO |   - Total records: 5334
2025-07-31 21:09:37 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 21:09:37 | INFO | Cleaning address data...
2025-07-31 21:09:37 | INFO | Address cleaning completed:
2025-07-31 21:09:37 | INFO |   - Valid addresses: 5334
2025-07-31 21:09:37 | INFO |   - Empty addresses: 0
2025-07-31 21:09:37 | INFO | Data processing completed successfully
2025-07-31 21:09:37 | INFO | Searching for: '宁波' (top_k=10, threshold=0.3)
2025-07-31 21:09:37 | INFO | Encoding 1 texts to vectors
2025-07-31 21:09:38 | INFO | Encoding completed in 0.88s
2025-07-31 21:09:38 | INFO | Vector shape: (1, 1024)
2025-07-31 21:09:38 | ERROR | Search failed: '<' not supported between instances of 'numpy.ndarray' and 'str'
2025-07-31 21:10:26 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 21:10:26 | INFO | Index loaded successfully in 0.01s
2025-07-31 21:10:26 | INFO | Index contains 5334 vectors
2025-07-31 21:10:26 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 21:10:28 | INFO | Model loaded successfully in 2.41s
2025-07-31 21:10:28 | INFO | Device: cuda
2025-07-31 21:10:28 | INFO | Max sequence length: 512
2025-07-31 21:10:28 | INFO | Loading data from ./data/process.xlsx
2025-07-31 21:10:31 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 21:10:31 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 21:10:31 | INFO | Address field validation:
2025-07-31 21:10:31 | INFO |   - Total records: 5334
2025-07-31 21:10:31 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 21:10:31 | INFO | Cleaning address data...
2025-07-31 21:10:31 | INFO | Address cleaning completed:
2025-07-31 21:10:31 | INFO |   - Valid addresses: 5334
2025-07-31 21:10:31 | INFO |   - Empty addresses: 0
2025-07-31 21:10:31 | INFO | Data processing completed successfully
2025-07-31 21:10:31 | INFO | Searching for: '宁波' (top_k=5, threshold=0.3)
2025-07-31 21:10:31 | INFO | Encoding 1 texts to vectors
2025-07-31 21:10:32 | INFO | Encoding completed in 0.91s
2025-07-31 21:10:32 | INFO | Vector shape: (1, 1024)
2025-07-31 21:10:32 | ERROR | Search failed: '<' not supported between instances of 'numpy.ndarray' and 'str'
2025-07-31 21:13:16 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 21:13:16 | INFO | Index loaded successfully in 0.01s
2025-07-31 21:13:16 | INFO | Index contains 5334 vectors
2025-07-31 21:13:16 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 21:13:18 | INFO | Model loaded successfully in 2.66s
2025-07-31 21:13:18 | INFO | Device: cuda
2025-07-31 21:13:18 | INFO | Max sequence length: 512
2025-07-31 21:13:18 | INFO | Loading data from ./data/process.xlsx
2025-07-31 21:13:21 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 21:13:21 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 21:13:21 | INFO | Address field validation:
2025-07-31 21:13:21 | INFO |   - Total records: 5334
2025-07-31 21:13:21 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 21:13:21 | INFO | Cleaning address data...
2025-07-31 21:13:21 | INFO | Address cleaning completed:
2025-07-31 21:13:21 | INFO |   - Valid addresses: 5334
2025-07-31 21:13:21 | INFO |   - Empty addresses: 0
2025-07-31 21:13:21 | INFO | Data processing completed successfully
2025-07-31 21:13:21 | INFO | Searching for: '宁波' (top_k=5, threshold=0.3)
2025-07-31 21:13:21 | INFO | Encoding 1 texts to vectors
2025-07-31 21:13:22 | INFO | Encoding completed in 0.95s
2025-07-31 21:13:22 | INFO | Vector shape: (1, 1024)
2025-07-31 21:13:22 | ERROR | Search failed: '<' not supported between instances of 'float' and 'str'
2025-07-31 21:14:09 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 21:14:09 | INFO | Index loaded successfully in 0.01s
2025-07-31 21:14:09 | INFO | Index contains 5334 vectors
2025-07-31 21:14:09 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 21:14:12 | INFO | Model loaded successfully in 2.53s
2025-07-31 21:14:12 | INFO | Device: cuda
2025-07-31 21:14:12 | INFO | Max sequence length: 512
2025-07-31 21:14:12 | INFO | Loading data from ./data/process.xlsx
2025-07-31 21:14:14 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 21:14:14 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 21:14:14 | INFO | Address field validation:
2025-07-31 21:14:14 | INFO |   - Total records: 5334
2025-07-31 21:14:14 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 21:14:14 | INFO | Cleaning address data...
2025-07-31 21:14:14 | INFO | Address cleaning completed:
2025-07-31 21:14:14 | INFO |   - Valid addresses: 5334
2025-07-31 21:14:14 | INFO |   - Empty addresses: 0
2025-07-31 21:14:14 | INFO | Data processing completed successfully
2025-07-31 21:14:14 | INFO | Searching for: '宁波' (top_k=5, threshold=0.3)
2025-07-31 21:14:14 | INFO | Encoding 1 texts to vectors
2025-07-31 21:14:15 | INFO | Encoding completed in 0.99s
2025-07-31 21:14:15 | INFO | Vector shape: (1, 1024)
2025-07-31 21:14:15 | INFO | Search completed in 1.008s, found 5 results
2025-07-31 21:16:21 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 21:16:21 | INFO | Index loaded successfully in 0.01s
2025-07-31 21:16:21 | INFO | Index contains 5334 vectors
2025-07-31 21:16:21 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 21:16:24 | INFO | Model loaded successfully in 2.43s
2025-07-31 21:16:24 | INFO | Device: cuda
2025-07-31 21:16:24 | INFO | Max sequence length: 512
2025-07-31 21:16:24 | INFO | Loading data from ./data/process.xlsx
2025-07-31 21:16:26 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 21:16:26 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 21:16:26 | INFO | Address field validation:
2025-07-31 21:16:26 | INFO |   - Total records: 5334
2025-07-31 21:16:26 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 21:16:26 | INFO | Cleaning address data...
2025-07-31 21:16:26 | INFO | Address cleaning completed:
2025-07-31 21:16:26 | INFO |   - Valid addresses: 5334
2025-07-31 21:16:26 | INFO |   - Empty addresses: 0
2025-07-31 21:16:26 | INFO | Data processing completed successfully
2025-07-31 21:16:26 | INFO | Searching for: '北京' (top_k=5, threshold=0.3)
2025-07-31 21:16:26 | INFO | Encoding 1 texts to vectors
2025-07-31 21:16:27 | INFO | Encoding completed in 0.89s
2025-07-31 21:16:27 | INFO | Vector shape: (1, 1024)
2025-07-31 21:16:27 | INFO | Search completed in 0.898s, found 5 results
2025-07-31 21:17:16 | INFO | Loading index from ./cache\company_index.faiss
2025-07-31 21:17:16 | INFO | Index loaded successfully in 0.01s
2025-07-31 21:17:16 | INFO | Index contains 5334 vectors
2025-07-31 21:17:16 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 21:17:19 | INFO | Model loaded successfully in 2.38s
2025-07-31 21:17:19 | INFO | Device: cuda
2025-07-31 21:17:19 | INFO | Max sequence length: 512
2025-07-31 21:17:19 | INFO | Loading data from ./data/process.xlsx
2025-07-31 21:17:21 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 21:17:21 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 21:17:21 | INFO | Address field validation:
2025-07-31 21:17:21 | INFO |   - Total records: 5334
2025-07-31 21:17:21 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 21:17:21 | INFO | Cleaning address data...
2025-07-31 21:17:21 | INFO | Address cleaning completed:
2025-07-31 21:17:21 | INFO |   - Valid addresses: 5334
2025-07-31 21:17:21 | INFO |   - Empty addresses: 0
2025-07-31 21:17:21 | INFO | Data processing completed successfully
2025-07-31 21:17:21 | INFO | Searching for: '北京' (top_k=10, threshold=0.3)
2025-07-31 21:17:21 | INFO | Encoding 1 texts to vectors
2025-07-31 21:17:22 | INFO | Encoding completed in 0.90s
2025-07-31 21:17:22 | INFO | Vector shape: (1, 1024)
2025-07-31 21:17:22 | INFO | Search completed in 0.920s, found 10 results
2025-07-31 22:30:59 | INFO | Encoding 1 texts to vectors
2025-07-31 22:30:59 | INFO | Encoding completed in 0.02s
2025-07-31 22:30:59 | INFO | Vector shape: (1, 1024)
2025-07-31 22:30:59 | INFO | Encoding 1 texts to vectors
2025-07-31 22:30:59 | INFO | Encoding completed in 0.02s
2025-07-31 22:30:59 | INFO | Vector shape: (1, 1024)
2025-07-31 22:32:41 | INFO | Initializing semantic search engine...
2025-07-31 22:32:41 | INFO | Step 1: Processing data...
2025-07-31 22:32:41 | INFO | Loading data from ./data/process.xlsx
2025-07-31 22:32:43 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 22:32:43 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 22:32:43 | INFO | Address field validation:
2025-07-31 22:32:43 | INFO |   - Total records: 5334
2025-07-31 22:32:43 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 22:32:43 | INFO | Cleaning address data...
2025-07-31 22:32:43 | INFO | Address cleaning completed:
2025-07-31 22:32:43 | INFO |   - Valid addresses: 5334
2025-07-31 22:32:43 | INFO |   - Empty addresses: 0
2025-07-31 22:32:43 | INFO | Data processing completed successfully
2025-07-31 22:32:43 | INFO | Step 2: Vectorizing addresses...
2025-07-31 22:32:43 | INFO | Generating new vectors...
2025-07-31 22:32:43 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 22:32:46 | INFO | Model loaded successfully in 2.39s
2025-07-31 22:32:46 | INFO | Device: cuda
2025-07-31 22:32:46 | INFO | Max sequence length: 512
2025-07-31 22:32:46 | INFO | Encoding 5334 texts to vectors
2025-07-31 22:33:04 | INFO | Encoding completed in 18.51s
2025-07-31 22:33:04 | INFO | Vector shape: (5334, 1024)
2025-07-31 22:33:04 | INFO | Vectors saved to ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-07-31 22:33:04 | INFO | Vector validation passed
2025-07-31 22:33:04 | INFO | Step 3: Building search index...
2025-07-31 22:33:04 | INFO | Building new index...
2025-07-31 22:33:04 | INFO | Building index for 5334 vectors with dimension 1024
2025-07-31 22:33:04 | INFO | Creating HNSW index with dimension 1024
2025-07-31 22:33:04 | INFO | HNSW parameters:
2025-07-31 22:33:04 | INFO |   - M: 16
2025-07-31 22:33:04 | INFO |   - efConstruction: 200
2025-07-31 22:33:04 | INFO |   - efSearch: 100
2025-07-31 22:33:04 | INFO | Adding vectors to index...
2025-07-31 22:33:30 | INFO | Initializing semantic search engine...
2025-07-31 22:33:30 | INFO | Step 1: Processing data...
2025-07-31 22:33:30 | INFO | Loading data from ./data/process.xlsx
2025-07-31 22:33:32 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-07-31 22:33:32 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-07-31 22:33:32 | INFO | Address field validation:
2025-07-31 22:33:32 | INFO |   - Total records: 5334
2025-07-31 22:33:32 | INFO |   - Null addresses: 0 (0.00%)
2025-07-31 22:33:32 | INFO | Cleaning address data...
2025-07-31 22:33:32 | INFO | Address cleaning completed:
2025-07-31 22:33:32 | INFO |   - Valid addresses: 5334
2025-07-31 22:33:32 | INFO |   - Empty addresses: 0
2025-07-31 22:33:32 | INFO | Data processing completed successfully
2025-07-31 22:33:32 | INFO | Step 2: Vectorizing addresses...
2025-07-31 22:33:32 | INFO | Generating new vectors...
2025-07-31 22:33:32 | INFO | Loading BGE-M3 model from ./bge-m3
2025-07-31 22:33:34 | INFO | Model loaded successfully in 2.40s
2025-07-31 22:33:34 | INFO | Device: cuda
2025-07-31 22:33:34 | INFO | Max sequence length: 512
2025-07-31 22:33:34 | INFO | Encoding 5334 texts to vectors
2025-07-31 22:33:53 | INFO | Encoding completed in 18.48s
2025-07-31 22:33:53 | INFO | Vector shape: (5334, 1024)
2025-07-31 22:33:53 | INFO | Vectors saved to ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-07-31 22:33:53 | INFO | Vector validation passed
2025-07-31 22:33:53 | INFO | Step 3: Building search index...
2025-07-31 22:33:53 | INFO | Building new index...
2025-07-31 22:33:53 | INFO | Building index for 5334 vectors with dimension 1024
2025-07-31 22:33:53 | INFO | Creating FLAT index with dimension 1024
2025-07-31 22:33:53 | INFO | Adding vectors to index...
2025-07-31 22:33:53 | INFO | Index built successfully in 0.01s
2025-07-31 22:33:53 | INFO | Index contains 5334 vectors
2025-07-31 22:33:53 | INFO | Saving index to ./cache\company_index.faiss
2025-07-31 22:33:53 | INFO | Index saved successfully in 0.01s
2025-07-31 22:33:53 | INFO | Index file size: 20.84 MB
2025-07-31 22:33:53 | INFO | Metadata saved to ./cache\company_metadata.pkl
2025-07-31 22:33:53 | INFO | Search engine initialized successfully in 23.29s
2025-07-31 22:33:53 | INFO | Ready to search 5334 company records
2025-08-02 11:35:32 | INFO | Initializing semantic search engine...
2025-08-02 11:35:32 | INFO | Step 1: Processing data...
2025-08-02 11:35:32 | INFO | Loading data from ./data/process.xlsx
2025-08-02 11:35:34 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-08-02 11:35:34 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-08-02 11:35:34 | INFO | Address field validation:
2025-08-02 11:35:34 | INFO |   - Total records: 5334
2025-08-02 11:35:34 | INFO |   - Null addresses: 0 (0.00%)
2025-08-02 11:35:34 | INFO | Cleaning address data...
2025-08-02 11:35:34 | INFO | Address cleaning completed:
2025-08-02 11:35:34 | INFO |   - Valid addresses: 5334
2025-08-02 11:35:34 | INFO |   - Empty addresses: 0
2025-08-02 11:35:34 | INFO | Data processing completed successfully
2025-08-02 11:35:34 | INFO | Step 2: Vectorizing addresses...
2025-08-02 11:35:34 | INFO | Generating new vectors...
2025-08-02 11:35:34 | INFO | Loading BGE-M3 model from ./bge-m3
2025-08-02 11:35:38 | INFO | Model loaded successfully in 3.82s
2025-08-02 11:35:38 | INFO | Device: cuda
2025-08-02 11:35:38 | INFO | Max sequence length: 512
2025-08-02 11:35:38 | INFO | Encoding 5334 texts to vectors
2025-08-02 11:35:58 | INFO | Encoding completed in 19.77s
2025-08-02 11:35:58 | INFO | Vector shape: (5334, 1024)
2025-08-02 11:35:58 | INFO | Vectors saved to ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-08-02 11:35:58 | INFO | Vector validation passed
2025-08-02 11:35:58 | INFO | Step 3: Building search index...
2025-08-02 11:35:58 | INFO | Building new index...
2025-08-02 11:35:58 | INFO | Building index for 5334 vectors with dimension 1024
2025-08-02 11:35:58 | INFO | Creating FLAT index with dimension 1024
2025-08-02 11:35:58 | INFO | Adding vectors to index...
2025-08-02 11:35:58 | INFO | Index built successfully in 0.01s
2025-08-02 11:35:58 | INFO | Index contains 5334 vectors
2025-08-02 11:35:58 | INFO | Saving index to ./cache\company_index.faiss
2025-08-02 11:35:58 | INFO | Index saved successfully in 0.01s
2025-08-02 11:35:58 | INFO | Index file size: 20.84 MB
2025-08-02 11:35:58 | INFO | Metadata saved to ./cache\company_metadata.pkl
2025-08-02 11:35:58 | INFO | Search engine initialized successfully in 26.10s
2025-08-02 11:35:58 | INFO | Ready to search 5334 company records
2025-08-02 11:36:36 | INFO | Loading index from ./cache\company_index.faiss
2025-08-02 11:36:36 | INFO | Index loaded successfully in 0.01s
2025-08-02 11:36:36 | INFO | Index contains 5334 vectors
2025-08-02 11:36:36 | INFO | Loading BGE-M3 model from ./bge-m3
2025-08-02 11:36:39 | INFO | Model loaded successfully in 2.55s
2025-08-02 11:36:39 | INFO | Device: cuda
2025-08-02 11:36:39 | INFO | Max sequence length: 512
2025-08-02 11:36:39 | INFO | Loading data from ./data/process.xlsx
2025-08-02 11:36:41 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-08-02 11:36:41 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-08-02 11:36:41 | INFO | Address field validation:
2025-08-02 11:36:41 | INFO |   - Total records: 5334
2025-08-02 11:36:41 | INFO |   - Null addresses: 0 (0.00%)
2025-08-02 11:36:41 | INFO | Cleaning address data...
2025-08-02 11:36:41 | INFO | Address cleaning completed:
2025-08-02 11:36:41 | INFO |   - Valid addresses: 5334
2025-08-02 11:36:41 | INFO |   - Empty addresses: 0
2025-08-02 11:36:41 | INFO | Data processing completed successfully
2025-08-02 11:36:41 | INFO | Searching for: '北京' (top_k=10, threshold=0.7)
2025-08-02 11:36:41 | INFO | Encoding 1 texts to vectors
2025-08-02 11:36:42 | INFO | Encoding completed in 0.96s
2025-08-02 11:36:42 | INFO | Vector shape: (1, 1024)
2025-08-02 11:36:42 | INFO | Search completed in 0.974s, found 0 results
2025-08-02 11:37:12 | INFO | Loading index from ./cache\company_index.faiss
2025-08-02 11:37:12 | INFO | Index loaded successfully in 0.01s
2025-08-02 11:37:12 | INFO | Index contains 5334 vectors
2025-08-02 11:37:12 | INFO | Loading BGE-M3 model from ./bge-m3
2025-08-02 11:37:14 | INFO | Model loaded successfully in 2.39s
2025-08-02 11:37:14 | INFO | Device: cuda
2025-08-02 11:37:14 | INFO | Max sequence length: 512
2025-08-02 11:37:14 | INFO | Loading data from ./data/process.xlsx
2025-08-02 11:37:17 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-08-02 11:37:17 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-08-02 11:37:17 | INFO | Address field validation:
2025-08-02 11:37:17 | INFO |   - Total records: 5334
2025-08-02 11:37:17 | INFO |   - Null addresses: 0 (0.00%)
2025-08-02 11:37:17 | INFO | Cleaning address data...
2025-08-02 11:37:17 | INFO | Address cleaning completed:
2025-08-02 11:37:17 | INFO |   - Valid addresses: 5334
2025-08-02 11:37:17 | INFO |   - Empty addresses: 0
2025-08-02 11:37:17 | INFO | Data processing completed successfully
2025-08-02 11:37:17 | INFO | Searching for: '北京' (top_k=10, threshold=0.5)
2025-08-02 11:37:17 | INFO | Encoding 1 texts to vectors
2025-08-02 11:37:17 | INFO | Encoding completed in 0.88s
2025-08-02 11:37:17 | INFO | Vector shape: (1, 1024)
2025-08-02 11:37:17 | INFO | Search completed in 0.891s, found 7 results
2025-08-02 15:58:44 | INFO | Initializing semantic search engine...
2025-08-02 15:58:44 | INFO | Step 1: Processing data...
2025-08-02 15:58:44 | INFO | Loading data from ./data/process.xlsx
2025-08-02 15:58:47 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-08-02 15:58:47 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-08-02 15:58:47 | INFO | Address field validation:
2025-08-02 15:58:47 | INFO |   - Total records: 5334
2025-08-02 15:58:47 | INFO |   - Null addresses: 0 (0.00%)
2025-08-02 15:58:47 | INFO | Cleaning address data...
2025-08-02 15:58:47 | INFO | Address cleaning completed:
2025-08-02 15:58:47 | INFO |   - Valid addresses: 5334
2025-08-02 15:58:47 | INFO |   - Empty addresses: 0
2025-08-02 15:58:47 | INFO | Data processing completed successfully
2025-08-02 15:58:47 | INFO | Step 2: Vectorizing addresses...
2025-08-02 15:58:47 | INFO | Vectors loaded from ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-08-02 15:58:47 | INFO | Using cached vectors from ./cache\company_addresses_company_vectors.npy
2025-08-02 15:58:47 | INFO | Vector validation passed
2025-08-02 15:58:47 | INFO | Step 3: Building search index...
2025-08-02 15:58:47 | INFO | Loading index from ./cache\company_index.faiss
2025-08-02 15:58:47 | INFO | Index loaded successfully in 0.01s
2025-08-02 15:58:47 | INFO | Index contains 5334 vectors
2025-08-02 15:58:47 | INFO | Metadata saved to ./cache\company_metadata.pkl
2025-08-02 15:58:47 | INFO | Search engine initialized successfully in 2.54s
2025-08-02 15:58:47 | INFO | Ready to search 5334 company records
2025-08-02 15:59:25 | INFO | Initializing semantic search engine...
2025-08-02 15:59:25 | INFO | Step 1: Processing data...
2025-08-02 15:59:25 | INFO | Loading data from ./data/process.xlsx
2025-08-02 15:59:27 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-08-02 15:59:27 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-08-02 15:59:27 | INFO | Address field validation:
2025-08-02 15:59:27 | INFO |   - Total records: 5334
2025-08-02 15:59:27 | INFO |   - Null addresses: 0 (0.00%)
2025-08-02 15:59:27 | INFO | Cleaning address data...
2025-08-02 15:59:27 | INFO | Address cleaning completed:
2025-08-02 15:59:27 | INFO |   - Valid addresses: 5334
2025-08-02 15:59:27 | INFO |   - Empty addresses: 0
2025-08-02 15:59:27 | INFO | Data processing completed successfully
2025-08-02 15:59:27 | INFO | Step 2: Vectorizing addresses...
2025-08-02 15:59:27 | INFO | Generating new vectors...
2025-08-02 15:59:27 | INFO | Loading BGE-M3 model from ./bge-m3
2025-08-02 15:59:31 | INFO | Model loaded successfully in 3.69s
2025-08-02 15:59:31 | INFO | Device: cuda
2025-08-02 15:59:31 | INFO | Max sequence length: 512
2025-08-02 15:59:31 | INFO | Encoding 5334 texts to vectors
2025-08-02 15:59:50 | INFO | Encoding completed in 19.43s
2025-08-02 15:59:50 | INFO | Vector shape: (5334, 1024)
2025-08-02 15:59:50 | INFO | Vectors saved to ./cache\company_addresses_company_vectors.npy, shape: (5334, 1024)
2025-08-02 15:59:50 | INFO | Vector validation passed
2025-08-02 15:59:50 | INFO | Step 3: Building search index...
2025-08-02 15:59:50 | INFO | Building new index...
2025-08-02 15:59:50 | INFO | Building index for 5334 vectors with dimension 1024
2025-08-02 15:59:50 | INFO | Creating FLAT index with dimension 1024
2025-08-02 15:59:50 | INFO | Adding vectors to index...
2025-08-02 15:59:50 | INFO | Index built successfully in 0.01s
2025-08-02 15:59:50 | INFO | Index contains 5334 vectors
2025-08-02 15:59:50 | INFO | Saving index to ./cache\company_index.faiss
2025-08-02 15:59:50 | INFO | Index saved successfully in 0.01s
2025-08-02 15:59:50 | INFO | Index file size: 20.84 MB
2025-08-02 15:59:50 | INFO | Metadata saved to ./cache\company_metadata.pkl
2025-08-02 15:59:50 | INFO | Search engine initialized successfully in 25.54s
2025-08-02 15:59:50 | INFO | Ready to search 5334 company records
2025-08-02 16:01:30 | INFO | Loading index from ./cache\company_index.faiss
2025-08-02 16:01:30 | INFO | Index loaded successfully in 0.01s
2025-08-02 16:01:30 | INFO | Index contains 5334 vectors
2025-08-02 16:01:30 | INFO | Loading BGE-M3 model from ./bge-m3
2025-08-02 16:01:32 | INFO | Model loaded successfully in 2.44s
2025-08-02 16:01:32 | INFO | Device: cuda
2025-08-02 16:01:32 | INFO | Max sequence length: 512
2025-08-02 16:01:32 | INFO | Loading data from ./data/process.xlsx
2025-08-02 16:01:34 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-08-02 16:01:34 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-08-02 16:01:34 | INFO | Address field validation:
2025-08-02 16:01:34 | INFO |   - Total records: 5334
2025-08-02 16:01:34 | INFO |   - Null addresses: 0 (0.00%)
2025-08-02 16:01:34 | INFO | Cleaning address data...
2025-08-02 16:01:34 | INFO | Address cleaning completed:
2025-08-02 16:01:34 | INFO |   - Valid addresses: 5334
2025-08-02 16:01:34 | INFO |   - Empty addresses: 0
2025-08-02 16:01:34 | INFO | Data processing completed successfully
2025-08-02 16:01:34 | INFO | Searching for: '杭州千帆有限责任公司' (top_k=10, threshold=0.7)
2025-08-02 16:01:34 | INFO | Encoding 1 texts to vectors
2025-08-02 16:01:35 | INFO | Encoding completed in 0.92s
2025-08-02 16:01:35 | INFO | Vector shape: (1, 1024)
2025-08-02 16:01:35 | INFO | Search completed in 0.932s, found 0 results
2025-08-02 16:08:22 | INFO | Loading index from ./cache\company_index.faiss
2025-08-02 16:08:22 | INFO | Index loaded successfully in 0.01s
2025-08-02 16:08:22 | INFO | Index contains 5334 vectors
2025-08-02 16:08:22 | INFO | Loading BGE-M3 model from ./bge-m3
2025-08-02 16:08:25 | INFO | Model loaded successfully in 2.51s
2025-08-02 16:08:25 | INFO | Device: cuda
2025-08-02 16:08:25 | INFO | Max sequence length: 512
2025-08-02 16:08:25 | INFO | Loading data from ./data/process.xlsx
2025-08-02 16:08:27 | INFO | Data loaded successfully: 5334 rows, 33 columns
2025-08-02 16:08:27 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-08-02 16:08:27 | INFO | Address field validation:
2025-08-02 16:08:27 | INFO |   - Total records: 5334
2025-08-02 16:08:27 | INFO |   - Null addresses: 0 (0.00%)
2025-08-02 16:08:27 | INFO | Cleaning address data...
2025-08-02 16:08:27 | INFO | Address cleaning completed:
2025-08-02 16:08:27 | INFO |   - Valid addresses: 5334
2025-08-02 16:08:27 | INFO |   - Empty addresses: 0
2025-08-02 16:08:27 | INFO | Data processing completed successfully
2025-08-02 16:08:27 | INFO | Searching for: '北京' (top_k=10, threshold=0.5)
2025-08-02 16:08:27 | INFO | Encoding 1 texts to vectors
2025-08-02 16:08:28 | INFO | Encoding completed in 0.98s
2025-08-02 16:08:28 | INFO | Vector shape: (1, 1024)
2025-08-02 16:08:28 | INFO | Search completed in 0.981s, found 7 results
2025-08-04 11:05:21 | INFO | Logging system initialized successfully
2025-08-04 11:05:21 | INFO | Log file: ./logs/semantic_search.log
2025-08-04 11:05:21 | INFO | Log level: INFO
2025-08-04 11:05:21 | INFO | === 日志功能测试开始 ===
2025-08-04 11:05:21 | INFO | 测试时间: 2025-08-04 11:05:21
2025-08-04 11:05:21 | INFO | 这是一条INFO级别的测试消息
2025-08-04 11:05:21 | WARNING | 这是一条WARNING级别的测试消息
2025-08-04 11:05:21 | ERROR | 这是一条ERROR级别的测试消息
2025-08-04 11:05:21 | INFO | 测试数据记录: {'query': '测试查询', 'results': 5, 'time': 1.23}
2025-08-04 11:05:21 | INFO | Searching for: '测试查询' (top_k=10, threshold=0.7)
2025-08-04 11:05:21 | INFO | Encoding 1 texts to vectors
2025-08-04 11:05:21 | INFO | Encoding completed in 0.10s
2025-08-04 11:05:21 | INFO | Vector shape: (1, 1024)
2025-08-04 11:05:21 | INFO | Search completed in 0.15s, found 3 results
2025-08-04 11:05:21 | INFO | === 日志功能测试结束 ===
2025-08-04 11:05:21 | INFO | Enhanced search engine initialized with multi-field vectorization
2025-08-04 11:05:21 | INFO | Multi-field vectorizer initialized
2025-08-04 11:05:21 | INFO | Primary fields: ['公司名称', '最新年报地址', '法定代表人']
2025-08-04 11:05:21 | INFO | Auxiliary fields: ['status', 'date', 'identifier']
2025-08-04 11:05:21 | INFO | Multi-vector index builder initialized (enabled: True)
2025-08-04 11:05:21 | INFO | === 搜索功能日志测试开始 ===
2025-08-04 11:05:21 | INFO | Initializing enhanced search engine...
2025-08-04 11:05:21 | INFO | Loading BGE-M3 model from ./bge-m3
2025-08-04 11:05:25 | INFO | Model loaded successfully in 3.80s
2025-08-04 11:05:25 | INFO | Device: cuda
2025-08-04 11:05:25 | INFO | Max sequence length: 512
2025-08-04 11:05:25 | INFO | Multi-field vectorizer model loaded
2025-08-04 11:05:25 | INFO | Loaded primary index from ./cache\multi_index_primary.faiss
2025-08-04 11:05:25 | INFO | Loaded status index from ./cache\multi_index_status.faiss
2025-08-04 11:05:25 | INFO | Loaded date index from ./cache\multi_index_date.faiss
2025-08-04 11:05:25 | INFO | Loaded identifier index from ./cache\multi_index_identifier.faiss
2025-08-04 11:05:25 | INFO | Multi-vector indexes loaded successfully
2025-08-04 11:05:25 | INFO | Loaded existing multi-field indexes
2025-08-04 11:05:25 | INFO | Loading data from ./data/浙江所有企业1.xlsx
2025-08-04 11:05:46 | INFO | Data loaded successfully: 50000 rows, 33 columns
2025-08-04 11:05:46 | INFO | Columns: ['公司名称', '登记状态', '法定代表人', '企业规模', '注册资本', '实缴资本', '成立日期', '核准日期', '营业期限', '所属省份', '所属城市', '所属区县', '公司类型', '国标行业门类', '国标行业大类', '国标行业中类', '曾用名', '英文名', '统一社会信用代码', '纳税人识别号', '注册号', '组织机构代码', '参保人数', '参保人数所属年报', '有效手机号', '更多电话', '注册地址', '最新年报地址', '通信地址', '网址', '邮箱', '其他邮箱', '经营范围']
2025-08-04 11:05:46 | INFO | Data validation:
2025-08-04 11:05:46 | INFO |   - Total records: 50000
2025-08-04 11:05:46 | INFO |   - 公司名称: 0 nulls (0.00%)
2025-08-04 11:05:46 | INFO |   - 最新年报地址: 0 nulls (0.00%)
2025-08-04 11:05:46 | INFO |   - 法定代表人: 0 nulls (0.00%)
2025-08-04 11:05:46 | INFO | Cleaning address data...
2025-08-04 11:05:46 | INFO | Cleaning address data...
2025-08-04 11:05:46 | INFO | Address cleaning completed:
2025-08-04 11:05:46 | INFO |   - Valid addresses: 50000
2025-08-04 11:05:46 | INFO |   - Empty addresses: 0
2025-08-04 11:05:46 | INFO | Data processing completed successfully
2025-08-04 11:05:46 | INFO | Searching for: '测试公司' (top_k=5, threshold=0.5)
2025-08-04 11:05:46 | INFO | Encoding query to vectors
2025-08-04 11:05:46 | INFO | Encoding 1 texts to vectors
2025-08-04 11:05:48 | INFO | Encoding completed in 1.82s
2025-08-04 11:05:48 | INFO | Vector shape: (1, 1024)
2025-08-04 11:05:48 | INFO | Encoding completed in 1.82s
2025-08-04 11:05:48 | INFO | Vector shape: (1024,)
