#!/usr/bin/env python3
"""
测试日志记录功能的独立脚本
"""

import os
import sys
import time
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_logging_system():
    """测试日志系统"""
    print("🧪 开始测试日志记录功能...")
    
    try:
        # 导入必要的模块
        from src.utils import load_config, setup_logging, logger
        
        # 加载配置
        config_path = 'config.yaml'
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
            
        config = load_config(config_path)
        print(f"✅ 配置文件加载成功: {config_path}")
        
        # 初始化日志系统
        setup_logging(config)
        print("✅ 日志系统初始化完成")
        
        # 测试不同级别的日志记录
        print("\n📝 测试日志记录...")
        logger.info("=== 日志功能测试开始 ===")
        logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("这是一条INFO级别的测试消息")
        logger.warning("这是一条WARNING级别的测试消息")
        logger.error("这是一条ERROR级别的测试消息")
        logger.debug("这是一条DEBUG级别的测试消息")
        
        # 测试带参数的日志
        test_data = {"query": "测试查询", "results": 5, "time": 1.23}
        logger.info(f"测试数据记录: {test_data}")
        
        # 模拟搜索日志
        logger.info("Searching for: '测试查询' (top_k=10, threshold=0.7)")
        logger.info("Encoding 1 texts to vectors")
        time.sleep(0.1)  # 模拟处理时间
        logger.info("Encoding completed in 0.10s")
        logger.info("Vector shape: (1, 1024)")
        logger.info("Search completed in 0.15s, found 3 results")
        
        logger.info("=== 日志功能测试结束 ===")
        
        # 检查日志文件
        log_file = config.get('logging', {}).get('file', './logs/semantic_search.log')
        if os.path.exists(log_file):
            print(f"✅ 日志文件存在: {log_file}")
            
            # 读取并显示最后几行日志
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            print(f"📊 日志文件总行数: {len(lines)}")
            
            # 显示最后10行
            last_lines = lines[-10:] if len(lines) >= 10 else lines
            print(f"\n📝 最后 {len(last_lines)} 行日志:")
            for i, line in enumerate(last_lines, 1):
                print(f"  {i:2d}: {line.strip()}")
                
            # 检查是否包含我们的测试消息
            test_content = ''.join(lines[-20:])  # 检查最后20行
            if "日志功能测试开始" in test_content and "日志功能测试结束" in test_content:
                print("✅ 测试消息已成功写入日志文件")
                return True
            else:
                print("⚠️ 测试消息未在日志文件中找到")
                return False
        else:
            print(f"❌ 日志文件不存在: {log_file}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_logging():
    """测试搜索功能的日志记录"""
    print("\n🔍 测试搜索功能日志记录...")
    
    try:
        from src.enhanced_search_engine import EnhancedSemanticSearchEngine
        from src.utils import logger
        
        # 初始化搜索引擎
        config_path = 'config.yaml'
        engine = EnhancedSemanticSearchEngine(config_path)
        
        logger.info("=== 搜索功能日志测试开始 ===")
        
        # 尝试初始化（可能会失败，但会产生日志）
        try:
            if engine.initialize():
                print("✅ 搜索引擎初始化成功")
                
                # 执行一个简单的搜索测试
                results = engine.search("测试公司", top_k=5, threshold=0.5)
                print(f"✅ 搜索完成，找到 {len(results)} 个结果")
            else:
                print("⚠️ 搜索引擎初始化失败（这是正常的，如果索引未构建）")
        except Exception as e:
            print(f"⚠️ 搜索测试失败: {e}")
            print("   这是正常的，如果索引文件不存在")
        
        logger.info("=== 搜索功能日志测试结束 ===")
        return True
        
    except Exception as e:
        print(f"❌ 搜索日志测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 日志功能测试脚本")
    print("=" * 50)
    
    # 测试基本日志功能
    basic_test_passed = test_logging_system()
    
    # 测试搜索日志功能
    search_test_passed = test_search_logging()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  基本日志功能: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"  搜索日志功能: {'✅ 通过' if search_test_passed else '❌ 失败'}")
    
    if basic_test_passed and search_test_passed:
        print("\n🎉 所有测试通过！日志功能正常工作。")
        sys.exit(0)
    else:
        print("\n⚠️ 部分测试失败，请检查配置和权限。")
        sys.exit(1)
