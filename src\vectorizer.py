"""
BGE-M3向量化模块

负责加载BGE-M3模型，将文本转换为向量表示，支持批处理和缓存
"""

import os
import time
import torch
import numpy as np
from typing import List, Dict, Any, Optional
from tqdm import tqdm
from loguru import logger
from transformers import AutoTokenizer, AutoModel
from .utils import save_vectors, load_vectors, validate_file_exists


class BGEVectorizer:
    """BGE-M3向量化器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化向量化器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.model_config = config.get('model', {})
        self.vector_config = config.get('vector_storage', {})
        
        # 模型配置
        self.model_path = self.model_config.get('path', './bge-m3')
        self.max_length = self.model_config.get('max_length', 512)
        self.batch_size = self.model_config.get('batch_size', 32)
        self.device = self.model_config.get('device', 'cpu')
        
        # 缓存配置
        self.cache_dir = self.vector_config.get('cache_dir', './cache')
        self.vectors_file = self.vector_config.get('vectors_file', 'company_vectors.npy')
        
        # 初始化变量
        self.model = None
        self.tokenizer = None
        self.is_loaded = False
        
    def load_model(self) -> None:
        """
        加载BGE-M3模型

        Raises:
            FileNotFoundError: 模型文件不存在
            RuntimeError: 模型加载失败
        """
        validate_file_exists(self.model_path, "BGE-M3 model directory")

        logger.info(f"Loading BGE-M3 model from {self.model_path}")
        start_time = time.time()

        try:
            # 检查是否有GPU可用
            if self.device == 'cuda' and not torch.cuda.is_available():
                logger.warning("CUDA not available, falling back to CPU")
                self.device = 'cpu'

            # 加载tokenizer和模型
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModel.from_pretrained(self.model_path)

            # 移动模型到指定设备
            self.model.to(self.device)
            self.model.eval()  # 设置为评估模式

            load_time = time.time() - start_time
            self.is_loaded = True

            logger.info(f"Model loaded successfully in {load_time:.2f}s")
            logger.info(f"Device: {self.device}")
            logger.info(f"Max sequence length: {self.max_length}")

        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise RuntimeError(f"Model loading failed: {e}")
    
    def encode_texts(self, texts: List[str], show_progress: bool = True) -> np.ndarray:
        """
        将文本列表编码为向量

        Args:
            texts: 文本列表
            show_progress: 是否显示进度条

        Returns:
            np.ndarray: 向量矩阵，形状为 (n_texts, embedding_dim)
        """
        if not self.is_loaded:
            self.load_model()

        if not texts:
            return np.array([])

        logger.info(f"Encoding {len(texts)} texts to vectors")
        start_time = time.time()

        # 过滤空文本
        valid_texts = []
        for text in texts:
            if text and text.strip():
                valid_texts.append(text.strip())
            else:
                valid_texts.append("空地址")  # 为空文本提供默认值

        try:
            # 批量编码
            all_vectors = []

            # 分批处理
            batch_size = self.batch_size
            total_batches = (len(valid_texts) + batch_size - 1) // batch_size

            if show_progress:
                progress_bar = tqdm(total=total_batches, desc="Encoding batches")

            with torch.no_grad():
                for i in range(0, len(valid_texts), batch_size):
                    batch_texts = valid_texts[i:i + batch_size]

                    # Tokenize
                    inputs = self.tokenizer(
                        batch_texts,
                        padding=True,
                        truncation=True,
                        max_length=self.max_length,
                        return_tensors='pt'
                    ).to(self.device)

                    # Forward pass
                    outputs = self.model(**inputs)

                    # 使用CLS token的输出作为句子表示
                    embeddings = outputs.last_hidden_state[:, 0, :]  # CLS token

                    # 根据配置决定是否进行L2归一化
                    if self.config.get('search', {}).get('normalize_vectors', True):
                        embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)

                    # 转换为numpy
                    batch_vectors = embeddings.cpu().numpy()
                    all_vectors.append(batch_vectors)

                    if show_progress:
                        progress_bar.update(1)

            if show_progress:
                progress_bar.close()

            # 合并所有批次的向量
            vectors = np.vstack(all_vectors)

            encode_time = time.time() - start_time
            logger.info(f"Encoding completed in {encode_time:.2f}s")
            logger.info(f"Vector shape: {vectors.shape}")

            return vectors

        except Exception as e:
            logger.error(f"Failed to encode texts: {e}")
            raise RuntimeError(f"Text encoding failed: {e}")
    
    def encode_single_text(self, text: str) -> np.ndarray:
        """
        编码单个文本
        
        Args:
            text: 输入文本
            
        Returns:
            np.ndarray: 文本向量
        """
        if not text or not text.strip():
            text = "空文本"
            
        vectors = self.encode_texts([text], show_progress=False)
        return vectors[0] if len(vectors) > 0 else np.array([])
    
    def vectorize_and_cache(
        self, 
        texts: List[str], 
        cache_key: str = "default",
        force_rebuild: bool = False
    ) -> np.ndarray:
        """
        向量化文本并缓存结果
        
        Args:
            texts: 文本列表
            cache_key: 缓存键名
            force_rebuild: 是否强制重建缓存
            
        Returns:
            np.ndarray: 向量矩阵
        """
        # 构建缓存文件路径
        cache_file = os.path.join(
            self.cache_dir, 
            f"{cache_key}_{self.vectors_file}"
        )
        
        # 尝试加载缓存
        if not force_rebuild:
            cached_vectors = load_vectors(cache_file)
            if cached_vectors is not None and len(cached_vectors) == len(texts):
                logger.info(f"Using cached vectors from {cache_file}")
                return cached_vectors
        
        # 生成新向量
        logger.info("Generating new vectors...")
        vectors = self.encode_texts(texts)
        
        # 保存到缓存
        save_vectors(vectors, cache_file)
        
        return vectors
    
    def get_embedding_dimension(self) -> int:
        """
        获取向量维度
        
        Returns:
            int: 向量维度
        """
        if not self.is_loaded:
            self.load_model()
        
        # 使用测试文本获取维度
        test_vector = self.encode_single_text("测试文本")
        return len(test_vector)
    
    def batch_process_with_memory_management(
        self, 
        texts: List[str], 
        max_batch_size: Optional[int] = None
    ) -> np.ndarray:
        """
        内存优化的批处理向量化
        
        Args:
            texts: 文本列表
            max_batch_size: 最大批处理大小
            
        Returns:
            np.ndarray: 向量矩阵
        """
        if max_batch_size is None:
            max_batch_size = self.batch_size
        
        if not self.is_loaded:
            self.load_model()
        
        logger.info(f"Processing {len(texts)} texts with batch size {max_batch_size}")
        
        all_vectors = []
        
        # 分批处理
        for i in tqdm(range(0, len(texts), max_batch_size), desc="Vectorizing batches"):
            batch_texts = texts[i:i + max_batch_size]
            batch_vectors = self.encode_texts(batch_texts, show_progress=False)
            all_vectors.append(batch_vectors)
            
            # 清理GPU内存（如果使用GPU）
            if self.device == 'cuda':
                torch.cuda.empty_cache()
        
        # 合并所有向量
        final_vectors = np.vstack(all_vectors)
        logger.info(f"Final vector shape: {final_vectors.shape}")
        
        return final_vectors
    
    def validate_vectors(self, vectors: np.ndarray) -> bool:
        """
        验证向量质量
        
        Args:
            vectors: 向量矩阵
            
        Returns:
            bool: 验证是否通过
        """
        if vectors.size == 0:
            logger.error("Empty vector matrix")
            return False
        
        # 检查NaN值
        if np.isnan(vectors).any():
            logger.error("Vectors contain NaN values")
            return False
        
        # 检查无穷大值
        if np.isinf(vectors).any():
            logger.error("Vectors contain infinite values")
            return False
        
        # 检查向量范数（应该接近1，因为已归一化）
        norms = np.linalg.norm(vectors, axis=1)
        if not np.allclose(norms, 1.0, atol=1e-3):
            logger.warning("Vectors may not be properly normalized")
        
        logger.info("Vector validation passed")
        return True
