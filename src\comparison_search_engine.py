#!/usr/bin/env python3
"""
对比搜索引擎
整合多层次向量化语义匹配与传统搜索方法，提供对比搜索功能
"""

import time
from typing import Dict, List, Any, Optional
from src.enhanced_search_engine import EnhancedSemanticSearchEngine
from src.traditional_search_engines import ExactKeywordSearchEngine, TFIDFSearchEngine
from src.utils import load_config


class ComparisonSearchEngine:
    """对比搜索引擎 - 整合三种搜索方法"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = load_config(config_path)
        
        # 初始化三种搜索引擎
        self.semantic_engine = EnhancedSemanticSearchEngine(config_path)
        self.exact_engine = ExactKeywordSearchEngine(self.config)
        self.tfidf_engine = TFIDFSearchEngine(self.config)
        
        self.initialized = False
    
    def initialize(self) -> bool:
        """初始化所有搜索引擎"""
        print("🚀 初始化对比搜索引擎...")
        
        success_count = 0
        
        # 初始化语义搜索引擎
        print("\n1️⃣ 初始化多层次向量化语义搜索引擎...")
        if self.semantic_engine.initialize():
            success_count += 1
        else:
            print("❌ 多层次向量化语义搜索引擎初始化失败")
        
        # 初始化精确关键词搜索引擎
        print("\n2️⃣ 初始化精确关键词搜索引擎...")
        if self.exact_engine.initialize():
            success_count += 1
        else:
            print("❌ 精确关键词搜索引擎初始化失败")
        
        # 初始化TF-IDF搜索引擎
        print("\n3️⃣ 初始化TF-IDF搜索引擎...")
        if self.tfidf_engine.initialize():
            success_count += 1
        else:
            print("❌ TF-IDF搜索引擎初始化失败")
        
        self.initialized = (success_count == 3)
        
        if self.initialized:
            print(f"\n✅ 对比搜索引擎初始化完成！成功初始化 {success_count}/3 个搜索引擎")
        else:
            print(f"\n⚠️ 对比搜索引擎部分初始化完成，成功初始化 {success_count}/3 个搜索引擎")
        
        return self.initialized
    
    def search_semantic_only(self, query: str, top_k: int = 10, threshold: Optional[float] = None,
                           search_strategy: str = 'auto', field_hints: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """仅使用多层次向量化语义搜索（保持原有功能）"""
        if not self.semantic_engine.initialized:
            raise ValueError("语义搜索引擎未初始化")
        
        return self.semantic_engine.search(
            query=query,
            top_k=top_k,
            threshold=threshold,
            search_strategy=search_strategy,
            field_hints=field_hints
        )
    
    def search_comparison(self, query: str, top_k: int = 10, threshold: Optional[float] = None) -> Dict[str, Any]:
        """执行对比搜索，同时使用三种方法"""
        if not self.initialized:
            raise ValueError("对比搜索引擎未完全初始化")
        
        print(f"🔍 执行对比搜索: '{query}'")
        print("=" * 60)
        
        results = {}
        
        # 1. 多层次向量化语义搜索
        print("1️⃣ 多层次向量化语义搜索...")
        try:
            start_time = time.time()
            semantic_results = self.semantic_engine.search(
                query=query,
                top_k=top_k,
                threshold=threshold,
                search_strategy='auto'
            )
            semantic_time = time.time() - start_time
            
            results['semantic'] = {
                'method': '多层次向量化语义匹配',
                'results': semantic_results,
                'total_found': len(semantic_results),
                'search_time': semantic_time
            }
            print(f"   ✅ 找到 {len(semantic_results)} 个结果，耗时 {semantic_time:.3f}秒")
            
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")
            results['semantic'] = {
                'method': '多层次向量化语义匹配',
                'results': [],
                'total_found': 0,
                'search_time': 0,
                'error': str(e)
            }
        
        # 2. 精确关键词搜索
        print("\n2️⃣ 精确关键词搜索...")
        try:
            exact_result = self.exact_engine.search(query, top_k, threshold)
            results['exact'] = exact_result
            print(f"   ✅ 找到 {exact_result['total_found']} 个结果，耗时 {exact_result['search_time']:.3f}秒")
            
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")
            results['exact'] = {
                'method': '精确关键词匹配',
                'results': [],
                'total_found': 0,
                'search_time': 0,
                'error': str(e)
            }
        
        # 3. TF-IDF搜索
        print("\n3️⃣ TF-IDF + 余弦相似度搜索...")
        try:
            tfidf_result = self.tfidf_engine.search(query, top_k, threshold)
            results['tfidf'] = tfidf_result
            print(f"   ✅ 找到 {tfidf_result['total_found']} 个结果，耗时 {tfidf_result['search_time']:.3f}秒")
            
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")
            results['tfidf'] = {
                'method': 'TF-IDF + 余弦相似度',
                'results': [],
                'total_found': 0,
                'search_time': 0,
                'error': str(e)
            }
        
        return results
    
    def display_comparison_results(self, results: Dict[str, Any], display_count: int = 5):
        """显示对比搜索结果"""
        print("\n" + "=" * 80)
        print("📊 搜索方法对比结果")
        print("=" * 80)
        
        # 显示总体统计
        print("\n📈 总体统计:")
        print("-" * 50)
        for method_key, result in results.items():
            method_name = result['method']
            total_found = result['total_found']
            search_time = result['search_time']
            
            if 'error' in result:
                print(f"{method_name}: ❌ 搜索失败 - {result['error']}")
            else:
                print(f"{method_name}: 找到 {total_found} 个结果，耗时 {search_time:.3f}秒")
        
        # 显示详细结果
        for method_key, result in results.items():
            if 'error' in result:
                continue
                
            method_name = result['method']
            method_results = result['results']
            
            print(f"\n🔍 {method_name} - 前{display_count}个结果:")
            print("-" * 60)
            
            if not method_results:
                print("   😔 未找到匹配结果")
                continue
            
            for i, item in enumerate(method_results[:display_count], 1):
                if method_key == 'semantic':
                    # 语义搜索结果格式
                    company_name = item.get('company_name', '未知')
                    similarity = item.get('similarity', 0)
                    city = item.get('city', '未知')
                    business_scope = item.get('business_scope', '未知')[:50] + '...' if len(item.get('business_scope', '')) > 50 else item.get('business_scope', '未知')
                    
                    print(f"   {i}. {company_name}")
                    print(f"      相似度: {similarity:.3f}")
                    print(f"      城市: {city}")
                    print(f"      经营范围: {business_scope}")
                    
                else:
                    # 传统搜索结果格式
                    company_name = item.get('company_name', '未知')
                    score = item.get('score', 0)
                    city = item.get('city', '未知')
                    business_scope = item.get('business_scope', '未知')[:50] + '...' if len(item.get('business_scope', '')) > 50 else item.get('business_scope', '未知')
                    
                    print(f"   {i}. {company_name}")
                    print(f"      分数: {score:.3f}")
                    print(f"      城市: {city}")
                    print(f"      经营范围: {business_scope}")
                
                print()
    
    def export_comparison_results(self, results: Dict[str, Any], output_file: str):
        """导出对比搜索结果"""
        import json
        
        # 准备导出数据
        export_data = {
            'query_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'methods': {}
        }
        
        for method_key, result in results.items():
            method_data = {
                'method_name': result['method'],
                'total_found': result['total_found'],
                'search_time': result['search_time'],
                'results': []
            }
            
            if 'error' in result:
                method_data['error'] = result['error']
            else:
                # 导出所有结果
                for item in result['results']:
                    if method_key == 'semantic':
                        # 语义搜索结果包含完整记录
                        method_data['results'].append(item)
                    else:
                        # 传统搜索结果包含company_record
                        method_data['results'].append(item)
            
            export_data['methods'][method_key] = method_data
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"📁 对比结果已导出到: {output_file}")
