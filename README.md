# 🔍 企业数据智能搜索系统

一个基于BGE-M3中文语义模型的本地企业数据搜索系统，支持多层次向量化语义匹配和传统搜索方法对比，让您快速找到相关企业信息。

## 🎯 系统简介

这是一个专为企业数据检索设计的智能搜索系统，它不同于传统的关键词搜索，而是通过深度学习技术理解您的查询意图，从海量企业数据中精准找到相关信息。系统集成了三种搜索方法，支持对比分析，帮助您选择最适合的搜索策略。

### 🔍 三种搜索方法

#### 1. 多层次向量化语义匹配（推荐）
- **技术原理**：基于BGE-M3中文向量模型的深度语义理解
- **特点**：能理解语义相似性，支持模糊匹配和概念关联
- **适用场景**：需要智能语义理解的复杂查询，如行业分析、概念搜索
- **优势**：理解同义词、相关概念，搜索结果更智能

#### 2. 精确关键词匹配
- **技术原理**：基于字符串精确匹配和字段权重计算
- **特点**：快速、准确，适合精确查询
- **适用场景**：已知确切关键词的精确查询，如企业名称查找
- **优势**：速度快，结果精确，适合已知信息的查找

#### 3. TF-IDF + 余弦相似度
- **技术原理**：基于词频-逆文档频率和余弦相似度计算
- **特点**：平衡精确性和召回率，适合文本相似度匹配
- **适用场景**：需要平衡精确性和覆盖面的搜索
- **优势**：经典算法，稳定可靠，适合大规模文本检索

### 💡 为什么选择这个系统？

**传统搜索的痛点**：
- 只能进行精确关键词匹配
- 无法理解同义词和相关概念
- 搜索结果要么太少要么太多
- 需要反复调整关键词才能找到想要的结果
- 无法对比不同搜索方法的效果

**我们的解决方案**：
- 🧠 **智能语义理解**：输入"科技公司"能找到"技术开发"、"软件研发"等相关企业
- 🎯 **智能匹配**：输入"杭州西湖"能找到"浙江省杭州市西湖区"的企业
- 📊 **精准控制**：通过相似度阈值控制结果的精确度
- ⚡ **快速响应**：GPU加速，秒级返回搜索结果
- 🔄 **方法对比**：同时使用三种方法搜索，对比分析效果差异
- 📈 **智能选择**：根据查询类型自动推荐最适合的搜索方法

## ✨ 系统特色

### 🚀 核心功能
- 🧠 **智能语义理解**：基于BGE-M3中文模型，理解查询意图而非简单关键词匹配
- 🏢 **多字段搜索**：同时搜索企业名称、地址、法人、登记状态、成立日期、纳税人识别号、手机号等7个字段
- 🔄 **三种搜索方法**：语义搜索、精确匹配、TF-IDF，支持单独使用或对比分析
- 📊 **对比分析**：一次查询展示三种方法的结果，便于效果对比
- 🎯 **精准控制**：可调节相似度阈值和搜索策略

### 🛡️ 安全与性能
- 🚀 **本地运行**：完全本地部署，数据安全有保障，无需联网
- ⚡ **GPU加速**：支持CUDA加速，搜索响应快速
- 💾 **智能缓存**：TF-IDF模型缓存，避免重复计算
- 🔧 **灵活配置**：丰富的配置选项，适应不同使用场景

### 📈 高效工作流
- 📈 **批量处理**：支持批量搜索，提高工作效率
- 💾 **结果导出**：支持CSV、JSON等多种格式导出，包含完整原始数据
- 🎛️ **多种索引构建**：支持分别构建语义索引、TF-IDF模型、精确匹配引擎
- 📋 **进度显示**：所有处理过程都有详细的进度条和状态信息

## 🚀 快速开始

### 第一步：环境准备

#### 系统要求
- **Python版本**：3.8+
- **操作系统**：Windows 10+
- **内存要求**：8GB+ (推荐16GB+)
- **显卡要求**：NVIDIA GPU with CUDA 11.0+ (可选，用于加速)
- **存储空间**：至少5GB可用空间

#### 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 如果使用GPU，确保安装了CUDA和cuDNN
# 检查CUDA是否可用
python -c "import torch; print(torch.cuda.is_available())"
```

#### 模型准备
确保BGE-M3模型文件位于 `./bge-m3/` 目录下。如果没有，请下载模型文件。

### 第二步：准备数据文件

#### 数据文件要求
- **文件格式**：Excel (.xlsx) 或 CSV (.csv)
- **存放位置**：`data` 文件夹
- **文件编码**：UTF-8（推荐）
- **数据规模**：支持1万到100万条记录

#### 必需的列名（7个核心字段）

| 列名 | 说明 | 示例 | 重要性 |
|------|------|------|--------|
| `公司名称` | 企业完整名称 | 杭州阿里巴巴网络技术有限公司 | ⭐⭐⭐⭐⭐ |
| `最新年报地址` | 企业注册或经营地址 | 浙江省杭州市余杭区文一西路969号 | ⭐⭐⭐⭐⭐ |
| `法定代表人` | 企业法定代表人姓名 | 张三 | ⭐⭐⭐⭐ |
| `登记状态` | 企业当前状态 | 存续、注销、吊销等 | ⭐⭐⭐ |
| `成立日期` | 企业成立时间 | 2020-01-15 | ⭐⭐⭐ |
| `纳税人识别号` | 统一社会信用代码 | 91330100MA28XYZ123 | ⭐⭐⭐ |
| `有效手机号` | 联系电话 | 13800138000 | ⭐⭐⭐ |

#### 数据质量建议
- **企业名称**：使用完整的法定名称，避免简称
- **地址信息**：包含完整的省市区信息，格式统一
- **日期格式**：统一使用YYYY-MM-DD格式
- **电话号码**：统一格式，去除特殊字符
- **数据完整性**：尽量减少空值，用"-"表示无数据
- **数据去重**：避免重复记录影响搜索效果

#### 配置数据文件
编辑 `config.yaml` 文件，指定数据文件路径：
```yaml
data:
  input_file: "./data/your_data_file.xlsx"  # 修改为您的数据文件
```

### 第三步：构建搜索索引

#### 基础索引构建
```bash
# 构建所有搜索方法的索引（推荐）
python main.py build-index --all

# 首次使用或数据更新后强制重建
python main.py build-index --all --force
```

#### 分别构建不同方法的索引
```bash
# 只构建语义搜索索引
python main.py build-index --semantic --force

# 只构建TF-IDF模型
python main.py build-index --tfidf --force

# 只初始化精确匹配搜索引擎
python main.py build-index --exact

# 构建多个特定方法
python main.py build-index --semantic --tfidf --force
```

#### 索引构建过程详解
1. **� 数据加载**：读取Excel/CSV文件，验证字段完整性
2. **🧹 数据清洗**：处理空值、格式化文本、去除特殊字符
3. **🔤 文本预处理**：中文分词、停用词过滤（TF-IDF）
4. **🧠 向量化**：使用BGE-M3模型生成语义向量（语义搜索）
5. **📈 模型构建**：构建TF-IDF词汇表和IDF权重（TF-IDF）
6. **🏗️ 索引构建**：创建FAISS向量索引（语义搜索）
7. **💾 缓存保存**：保存索引文件和模型到缓存目录



### 第四步：开始智能搜索

#### 基础搜索命令
```bash
# 标准语义搜索（默认模式）
python main.py search "杭州科技"

# 对比搜索（同时使用三种方法）
python main.py search "杭州科技" --compare

# 自定义显示结果数量
python main.py search "杭州科技" --compare --display-count 3
```

#### 单一方法搜索
```bash
# 只使用语义搜索（默认）
python main.py search "杭州科技" --top-k 10

# 调整相似度阈值
python main.py search "杭州科技" --threshold 0.5

# 使用字段提示
python main.py search "阿里巴巴" --field-hints 公司名称
python main.py search "张三" --field-hints 法定代表人
python main.py search "存续" --field-hints 登记状态
```

#### 搜索策略选择
```bash
# 自动策略（推荐）
python main.py search "互联网" --strategy auto

# 仅搜索主要字段
python main.py search "科技" --strategy primary_only

# 加权融合所有字段
python main.py search "电商" --strategy weighted_fusion

# 级联搜索策略
python main.py search "AI" --strategy cascade
```

## 📖 详细使用指南

### 🔍 对比搜索功能详解

#### 对比搜索的优势
对比搜索是本系统的核心特色，它能让您：
- **一次查询，三种结果**：同时看到语义搜索、精确匹配、TF-IDF的结果
- **效果对比**：直观比较不同方法的搜索效果和性能
- **智能选择**：根据结果质量选择最适合的搜索方法
- **学习优化**：了解不同查询类型适合的搜索策略

#### 对比搜索示例
```bash
# 基础对比搜索
python main.py search "阿里巴巴" --compare

# 自定义显示数量的对比搜索
python main.py search "杭州科技" --compare --display-count 5

# 调整阈值的对比搜索
python main.py search "软件开发" --compare --threshold 0.4 --display-count 3
```

#### 对比搜索结果解读
```
📊 搜索方法对比结果
================================================================================

📈 总体统计:
多层次向量化语义匹配: 找到 10 个结果，耗时 1.403秒
精确关键词匹配: 找到 1 个结果，耗时 2.330秒
TF-IDF + 余弦相似度: 找到 0 个结果，耗时 0.561秒

🔍 多层次向量化语义匹配 - 前3个结果:
   1. 杭州宝轩网络科技有限公司 (相似度: 0.502)
   2. 杭州云轩互联网科技有限公司 (相似度: 0.502)
   3. 望元（杭州）智能科技产业有限公司 (相似度: 0.437)

🔍 精确关键词匹配 - 前3个结果:
   1. 杭州奥嘉娜品牌管理有限公司 (分数: 0.720)

🔍 TF-IDF + 余弦相似度 - 前3个结果:
   😔 未找到匹配结果
```

### 🎯 实用搜索技巧

#### 按搜索目标分类

| 搜索目标 | 搜索示例 | 推荐方法 | 推荐参数 | 说明 |
|----------|----------|----------|----------|------|
| **精确企业查找** | `杭州阿里巴巴网络技术有限公司` | 精确匹配 | `--threshold 0.8` | 已知完整企业名称 |
| **行业企业发现** | `人工智能`、`新能源汽车` | 语义搜索 | `--threshold 0.4 --top-k 20` | 概念性行业搜索 |
| **地区企业调研** | `杭州西湖区科技` | 语义搜索 | `--field-hints 最新年报地址` | 地理位置相关 |
| **法人关联企业** | `张三` | 精确匹配 | `--field-hints 法定代表人` | 查找同一法人企业 |
| **企业状态筛选** | `注销`、`吊销` | 精确匹配 | `--field-hints 登记状态` | 特定状态企业 |
| **成立时间筛选** | `2020年`、`2021年成立` | TF-IDF | `--field-hints 成立日期` | 时间范围查询 |
| **文本相似搜索** | `电子商务平台运营` | TF-IDF | `--threshold 0.3` | 文本描述匹配 |

#### 搜索词优化技巧

**✅ 语义搜索推荐词汇**：
- **行业概念**：`人工智能`、`新零售`、`绿色能源`、`金融科技`
- **技术领域**：`云计算`、`大数据`、`区块链`、`物联网`
- **商业模式**：`电子商务`、`共享经济`、`数字化转型`
- **产品服务**：`移动支付`、`在线教育`、`智能制造`

**✅ 精确匹配推荐词汇**：
- **企业全名**：`杭州阿里巴巴网络技术有限公司`
- **法人姓名**：`马云`、`张三`、`李四`
- **具体地址**：`杭州市西湖区`、`上海市浦东新区`
- **企业状态**：`存续`、`注销`、`吊销`、`迁出`

**✅ TF-IDF推荐词汇**：
- **业务描述**：`软件开发与技术服务`、`电子产品销售`
- **经营范围**：`计算机软硬件开发`、`网络技术服务`
- **复合查询**：`杭州 软件 科技 有限公司`

**❌ 不推荐的搜索词**：
- **过于通用**：`公司`、`有限`、`企业`、`技术`
- **单个字符**：`a`、`1`、`中`、`国`
- **无意义组合**：`aaa`、`123`、`测试`、`xxx`
- **特殊符号**：`@#$%`、`***`、`---`

#### 三种搜索方法的适用场景

| 搜索方法 | 最佳适用场景 | 优势 | 劣势 | 推荐阈值 |
|----------|--------------|------|------|----------|
| **语义搜索** | 概念性查询、行业分析、模糊搜索 | 理解语义、概念关联、智能匹配 | 可能过于宽泛、计算较慢 | 0.4-0.6 |
| **精确匹配** | 已知信息查询、精确查找 | 速度快、结果精确、无歧义 | 无法理解同义词、过于严格 | 0.6-0.8 |
| **TF-IDF** | 文本相似度、平衡查询 | 经典算法、稳定可靠、平衡性好 | 无语义理解、依赖词频 | 0.3-0.5 |

### 🔧 完整命令参考

#### 系统管理命令

```bash
# 检查系统状态和配置
python main.py status

# 构建所有搜索方法的索引（推荐）
python main.py build-index --all

# 强制重建所有索引
python main.py build-index --all --force

# 分别构建不同方法的索引
python main.py build-index --semantic --force    # 只构建语义搜索索引
python main.py build-index --tfidf --force       # 只构建TF-IDF模型
python main.py build-index --exact               # 只初始化精确匹配引擎

# 构建多个特定方法
python main.py build-index --semantic --tfidf --force
```

#### 基础搜索命令

```bash
# 标准语义搜索（默认模式）
python main.py search "搜索词"

# 对比搜索（三种方法同时使用）
python main.py search "搜索词" --compare

# 自定义显示结果数量
python main.py search "搜索词" --compare --display-count 5

# 调整相似度阈值
python main.py search "搜索词" --threshold 0.5

# 返回更多结果
python main.py search "搜索词" --top-k 20
```

#### 高级搜索命令

```bash
# 字段定向搜索
python main.py search "张三" --field-hints 法定代表人
python main.py search "杭州" --field-hints 最新年报地址
python main.py search "存续" --field-hints 登记状态
python main.py search "2020" --field-hints 成立日期
python main.py search "91330100" --field-hints 纳税人识别号

# 搜索策略选择（仅语义搜索）
python main.py search "互联网" --strategy auto           # 自动选择（推荐）
python main.py search "科技" --strategy primary_only     # 仅搜索主要字段
python main.py search "电商" --strategy weighted_fusion  # 加权融合所有字段
python main.py search "AI" --strategy cascade            # 级联搜索策略

# 精确搜索（高阈值）
python main.py search "阿里巴巴" --threshold 0.8 --top-k 5

# 广泛搜索（低阈值）
python main.py search "科技公司" --threshold 0.3 --top-k 50

# 组合参数搜索
python main.py search "杭州科技" --threshold 0.4 --top-k 15 --field-hints 公司名称
```

#### 结果导出命令

```bash
# 导出为CSV格式
python main.py search "科技" --format csv --output 科技企业.csv

# 导出为JSON格式
python main.py search "杭州" --format json --output 杭州企业.json

# 对比搜索结果导出
python main.py search "阿里巴巴" --compare --format csv --output 对比结果.csv

# 批量搜索结果导出
python main.py batch-search queries.txt --output 批量结果.csv --format csv
```

#### 批量搜索详解

**创建查询文件** (`queries.txt`)：
```
杭州科技公司
上海金融企业
北京互联网公司
深圳制造业
广州电商平台
成都软件开发
西安新能源
武汉生物科技
```

**执行批量搜索**：
```bash
# 基础批量搜索
python main.py batch-search queries.txt

# 批量搜索并导出
python main.py batch-search queries.txt --output 批量结果.csv

# 批量搜索自定义参数
python main.py batch-search queries.txt --threshold 0.5 --top-k 10

# 批量对比搜索
python main.py batch-search queries.txt --compare --output 批量对比结果.csv
```

#### 实用命令组合示例

```bash
# 企业尽职调查
python main.py search "目标企业名称" --compare --threshold 0.7 --display-count 5

# 行业竞争分析
python main.py search "人工智能" --threshold 0.4 --top-k 50 --format csv --output AI企业分析.csv

# 地区企业调研
python main.py search "杭州西湖区" --field-hints 最新年报地址 --top-k 30 --format json --output 西湖区企业.json

# 法人关联企业查询
python main.py search "张三" --field-hints 法定代表人 --threshold 0.8 --format csv --output 张三关联企业.csv

# 企业状态监控
python main.py search "注销" --field-hints 登记状态 --top-k 100 --format csv --output 注销企业列表.csv
```

### ⚙️ 搜索策略详解

#### 语义搜索策略（仅适用于语义搜索）

| 策略 | 适用场景 | 工作原理 | 优势 | 劣势 |
|------|----------|----------|------|------|
| `auto` | 通用搜索（推荐） | 系统根据查询内容自动选择最佳策略 | 智能化、适应性强 | 可能不够精确 |
| `primary_only` | 精确搜索 | 只搜索核心字段（公司名称、地址、法人） | 结果精确、速度快 | 覆盖面有限 |
| `weighted_fusion` | 全面搜索 | 综合所有7个字段信息，按权重融合 | 覆盖全面、信息丰富 | 可能引入噪音 |
| `cascade` | 层次搜索 | 先搜索主要字段，再搜索辅助字段 | 层次清晰、结果有序 | 计算复杂度高 |

#### 策略选择建议

| 查询类型 | 推荐策略 | 原因 | 示例 |
|----------|----------|------|------|
| **企业名称查询** | `primary_only` | 名称在主要字段中，精确匹配 | `阿里巴巴`、`腾讯` |
| **地理位置查询** | `primary_only` | 地址在主要字段中 | `杭州西湖区`、`上海浦东` |
| **行业概念查询** | `weighted_fusion` | 需要综合多个字段信息 | `人工智能`、`新能源` |
| **复合查询** | `auto` | 让系统智能选择 | `杭州科技公司`、`北京互联网` |
| **探索性查询** | `cascade` | 层次化搜索，发现更多相关信息 | `区块链`、`大数据` |

### 📊 完整参数说明

#### 核心搜索参数

| 参数 | 简写 | 类型 | 说明 | 默认值 | 取值范围 | 示例 |
|------|------|------|------|--------|----------|------|
| `--top-k` | `-k` | 整数 | 返回结果数量 | 10 | 1-100 | `--top-k 20` |
| `--threshold` | `-t` | 浮点数 | 相似度阈值 | 0.3 | 0.0-1.0 | `--threshold 0.5` |
| `--strategy` | `-s` | 字符串 | 搜索策略（仅语义搜索） | auto | auto/primary_only/weighted_fusion/cascade | `--strategy primary_only` |
| `--field-hints` | `-f` | 字符串 | 字段提示 | 无 | 见字段列表 | `--field-hints 公司名称` |

#### 对比搜索参数

| 参数 | 类型 | 说明 | 默认值 | 示例 |
|------|------|------|--------|------|
| `--compare` | 标志 | 启用对比搜索模式 | False | `--compare` |
| `--display-count` | 整数 | 每种方法显示的结果数量 | 5 | `--display-count 3` |

#### 输出控制参数

| 参数 | 简写 | 类型 | 说明 | 默认值 | 可选值 | 示例 |
|------|------|------|------|--------|--------|------|
| `--format` | | 字符串 | 输出格式 | table | table/csv/json | `--format csv` |
| `--output` | `-o` | 字符串 | 输出文件路径 | 无 | 任意文件路径 | `--output 结果.csv` |

#### 索引构建参数

| 参数 | 类型 | 说明 | 默认值 | 示例 |
|------|------|------|--------|------|
| `--all` | 标志 | 构建所有方法的索引 | False | `--all` |
| `--semantic` | 标志 | 构建语义搜索索引 | False | `--semantic` |
| `--tfidf` | 标志 | 构建TF-IDF模型 | False | `--tfidf` |
| `--exact` | 标志 | 初始化精确匹配引擎 | False | `--exact` |
| `--force` | 标志 | 强制重建索引 | False | `--force` |

#### 可用字段提示

| 字段名 | 说明 | 适用搜索内容 | 示例查询 |
|--------|------|--------------|----------|
| `公司名称` | 企业名称 | 企业名称、品牌名 | `阿里巴巴`、`腾讯` |
| `最新年报地址` | 企业地址 | 地理位置、区域 | `杭州西湖区`、`上海浦东新区` |
| `法定代表人` | 法人姓名 | 人名 | `马云`、`张三` |
| `登记状态` | 企业状态 | 企业状态 | `存续`、`注销`、`吊销` |
| `成立日期` | 成立时间 | 时间、年份 | `2020`、`2021年` |
| `纳税人识别号` | 统一社会信用代码 | 企业代码 | `91330100`、`MA28` |
| `有效手机号` | 联系电话 | 电话号码 | `138`、`1380013` |

#### 阈值设置指南

| 阈值范围 | 搜索特点 | 结果特征 | 适用场景 | 推荐用途 |
|----------|----------|----------|----------|----------|
| **0.8-1.0** | 极其严格 | 极少但极相关 | 精确查找已知企业 | 企业验证、精确匹配 |
| **0.6-0.8** | 较为严格 | 相关度高的结果 | 常规搜索 | 日常查询、目标明确 |
| **0.4-0.6** | 适中宽松 | 平衡相关度和数量 | 探索性搜索 | 行业分析、概念搜索 |
| **0.2-0.4** | 比较宽松 | 更多潜在相关结果 | 广泛调研 | 市场调研、全面分析 |
| **0.0-0.2** | 极其宽松 | 大量结果需筛选 | 全面分析 | 数据挖掘、趋势分析 |


## 💡 高级使用技巧

### 🎯 参数组合策略

#### 按使用场景选择参数

| 使用场景 | 推荐参数组合 | 适用情况 | 预期结果 |
|----------|--------------|----------|----------|
| **精确查找** | `--threshold 0.8 --strategy primary_only --top-k 5` | 寻找特定企业 | 少而精的结果 |
| **广泛调研** | `--threshold 0.3 --strategy weighted_fusion --top-k 50` | 行业分析 | 全面的相关企业 |
| **快速预览** | `--threshold 0.6 --top-k 10` | 初步了解 | 平衡的结果数量 |
| **地区搜索** | `--threshold 0.4 --field-hints 最新年报地址 --top-k 20` | 区域分析 | 地理位置相关 |
| **法人查询** | `--threshold 0.7 --field-hints 法定代表人 --top-k 15` | 关联企业 | 同一法人企业 |
| **对比分析** | `--compare --threshold 0.5 --display-count 5` | 方法对比 | 三种方法结果对比 |
| **批量处理** | `batch-search queries.txt --threshold 0.4 --top-k 20` | 大量查询 | 批量搜索结果 |

#### 实际应用场景示例

**1. 企业尽职调查**
```bash
# 全面了解目标企业
python main.py search "目标企业名称" --compare --threshold 0.7 --display-count 3

# 查找关联企业
python main.py search "法定代表人姓名" --field-hints 法定代表人 --threshold 0.8
```

**2. 行业竞争分析**
```bash
# 发现行业内企业
python main.py search "人工智能" --threshold 0.4 --top-k 50 --format csv --output AI行业分析.csv

# 对比不同搜索方法的效果
python main.py search "新能源汽车" --compare --display-count 10
```

**3. 地区投资调研**
```bash
# 特定区域企业分析
python main.py search "杭州西湖区科技" --field-hints 最新年报地址 --top-k 30

# 批量地区分析
echo -e "杭州科技\n上海金融\n深圳制造\n北京互联网" > regions.txt
python main.py batch-search regions.txt --format csv --output 地区分析.csv
```

**4. 企业状态监控**
```bash
# 查找注销企业
python main.py search "注销" --field-hints 登记状态 --top-k 100 --format csv --output 注销企业.csv

# 监控特定时期成立的企业
python main.py search "2023年" --field-hints 成立日期 --threshold 0.6 --top-k 50
```

### 🚀 性能优化建议

#### 硬件配置优化

**GPU加速配置**：
```yaml
# config.yaml - 高性能GPU配置
model:
  device: "cuda"
  batch_size: 64          # 大显存可以用更大批次
  max_length: 512
```

**CPU优化配置**：
```yaml
# config.yaml - CPU优化配置
model:
  device: "cpu"
  batch_size: 16          # CPU使用较小批次
  max_length: 256         # 减少序列长度提高速度
```