"""
工具函数模块

提供配置加载、日志设置、文件操作等通用功能
"""

import os
import yaml
import pickle
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger


def setup_logger(name: str):
    """
    设置模块级别的logger

    Args:
        name: 模块名称

    Returns:
        logger: 配置好的logger实例
    """
    return logger


def load_config(config_path: str) -> Dict[str, Any]:
    """加载YAML配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        raise ValueError(f"Failed to load config from {config_path}: {e}")


def setup_logging(config: Dict[str, Any]) -> None:
    """设置日志配置"""
    log_config = config.get('logging', {})
    log_level = log_config.get('level', 'INFO')
    log_format = log_config.get('format', '{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}')
    log_file = log_config.get('file', './logs/semantic_search.log')
    
    # 确保日志目录存在
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # 移除默认处理器
    logger.remove()
    
    # 添加控制台处理器
    logger.add(
        sink=lambda msg: print(msg, end=''),
        format=log_format,
        level=log_level,
        colorize=True
    )
    
    # 添加文件处理器
    logger.add(
        sink=log_file,
        format=log_format,
        level=log_level,
        rotation="10 MB",
        retention="7 days",
        encoding="utf-8"
    )


def ensure_dir(path: str) -> None:
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)


def save_vectors(vectors: np.ndarray, filepath: str) -> None:
    """保存向量到文件"""
    ensure_dir(os.path.dirname(filepath))
    np.save(filepath, vectors)
    logger.info(f"Vectors saved to {filepath}, shape: {vectors.shape}")


def load_vectors(filepath: str) -> Optional[np.ndarray]:
    """从文件加载向量"""
    if not os.path.exists(filepath):
        return None
    vectors = np.load(filepath)
    logger.info(f"Vectors loaded from {filepath}, shape: {vectors.shape}")
    return vectors


def save_metadata(metadata: Any, filepath: str) -> None:
    """保存元数据到文件"""
    ensure_dir(os.path.dirname(filepath))
    with open(filepath, 'wb') as f:
        pickle.dump(metadata, f)
    logger.info(f"Metadata saved to {filepath}")


def load_metadata(filepath: str) -> Optional[Any]:
    """从文件加载元数据"""
    if not os.path.exists(filepath):
        return None
    with open(filepath, 'rb') as f:
        metadata = pickle.load(f)
    logger.info(f"Metadata loaded from {filepath}")
    return metadata


def clean_text(text: str) -> str:
    """清洗文本数据"""
    if not isinstance(text, str):
        return ""
    
    # 移除多余空白字符
    text = ' '.join(text.split())
    
    # 移除特殊字符（保留中文、英文、数字、常用标点）
    import re
    text = re.sub(r'[^\u4e00-\u9fff\w\s\-\.\,\(\)\[\]\/]', '', text)
    
    return text.strip()


def validate_file_exists(filepath: str, description: str = "File") -> None:
    """验证文件是否存在"""
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"{description} not found: {filepath}")


def get_file_size_mb(filepath: str) -> float:
    """获取文件大小（MB）"""
    if not os.path.exists(filepath):
        return 0.0
    return os.path.getsize(filepath) / (1024 * 1024)


def format_time(seconds: float) -> str:
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        return f"{seconds/60:.1f}m"
    else:
        return f"{seconds/3600:.1f}h"
