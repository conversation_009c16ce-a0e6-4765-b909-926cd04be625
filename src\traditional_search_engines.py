#!/usr/bin/env python3
"""
传统搜索引擎实现
包含精确关键词匹配和TF-IDF + 余弦相似度搜索方法
"""

import re
import math
import time
import pickle
import os
import yaml
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
from collections import Counter, defaultdict
from tqdm import tqdm

# 尝试导入jieba，如果没有则使用简单分词
try:
    import jieba
    HAS_JIEBA = True
except ImportError:
    HAS_JIEBA = False


class ExactKeywordSearchEngine:
    """精确关键词匹配搜索引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.data = None
        self.initialized = False

        # 从配置文件读取统一的字段配置
        self._load_field_config()

    def _load_field_config(self):
        """从配置文件加载字段配置（统一7个字段）"""
        try:
            # 如果config是字典，直接使用；如果是路径，则加载文件
            if isinstance(self.config, str):
                with open(self.config, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
            else:
                config_data = self.config

            multi_field_config = config_data.get('data', {}).get('multi_field', {})

            # 获取主要字段配置
            primary_fields = multi_field_config.get('primary_fields', {})
            # 获取辅助字段配置
            auxiliary_fields = multi_field_config.get('auxiliary_fields', {})
            # 获取传统搜索方法的额外字段配置
            traditional_fields = multi_field_config.get('traditional_search_fields', {})

            self.text_fields = []
            self.field_weights = {}

            # 1. 添加主要字段（公司名称、最新年报地址、法定代表人）
            for field_name, field_config in primary_fields.items():
                self.text_fields.append(field_name)
                if isinstance(field_config, dict):
                    self.field_weights[field_name] = field_config.get('exact_weight', 0.1)
                else:
                    self.field_weights[field_name] = field_config

            # 2. 添加辅助字段（登记状态、成立日期）
            if 'status' in auxiliary_fields:
                status_field = auxiliary_fields['status']
                self.text_fields.append(status_field)
                self.field_weights[status_field] = traditional_fields.get(status_field, {}).get('exact_weight', 0.1)

            if 'date' in auxiliary_fields:
                date_field = auxiliary_fields['date']
                self.text_fields.append(date_field)
                self.field_weights[date_field] = traditional_fields.get(date_field, {}).get('exact_weight', 0.05)

            # 3. 添加标识符字段（纳税人识别号、有效手机号）
            if 'identifier' in auxiliary_fields:
                identifier_fields = auxiliary_fields['identifier']
                if isinstance(identifier_fields, list):
                    for id_field in identifier_fields:
                        self.text_fields.append(id_field)
                        self.field_weights[id_field] = traditional_fields.get(id_field, {}).get('exact_weight', 0.1)
                else:
                    self.text_fields.append(identifier_fields)
                    self.field_weights[identifier_fields] = traditional_fields.get(identifier_fields, {}).get('exact_weight', 0.1)

            print(f"   📋 精确匹配字段 (共{len(self.text_fields)}个): {self.text_fields}")
            print(f"   ⚖️ 字段权重: {self.field_weights}")

        except Exception as e:
            print(f"   ⚠️ 字段配置加载失败，使用默认配置: {e}")
            # 使用默认的7个字段配置
            self.text_fields = ['公司名称', '最新年报地址', '法定代表人', '登记状态', '成立日期', '纳税人识别号', '有效手机号']
            self.field_weights = {
                '公司名称': 0.4,
                '最新年报地址': 0.3,
                '法定代表人': 0.3,
                '登记状态': 0.1,
                '成立日期': 0.05,
                '纳税人识别号': 0.1,
                '有效手机号': 0.1
            }

    def initialize(self) -> bool:
        """初始化搜索引擎"""
        try:
            # 加载数据
            data_file = self.config.get('data_file', './data/浙江所有企业1.xlsx')
            print(f"📊 加载企业数据: {data_file}")
            
            self.data = pd.read_excel(data_file)
            print(f"   - 加载了 {len(self.data)} 条企业记录")
            
            # 预处理数据
            self._preprocess_data()
            
            self.initialized = True
            print("✅ 精确关键词搜索引擎初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 精确关键词搜索引擎初始化失败: {e}")
            return False
    
    def _preprocess_data(self):
        """预处理数据，合并文本字段"""
        self.combined_texts = []

        print("   🔄 预处理企业数据...")
        for _, row in tqdm(self.data.iterrows(), total=len(self.data), desc="处理数据"):
            combined_text = ""
            for field in self.text_fields:
                if field in row and pd.notna(row[field]) and str(row[field]).strip() != '-':
                    field_text = str(row[field]).strip()
                    combined_text += field_text + " "
            self.combined_texts.append(combined_text.strip())

        print(f"   ✅ 数据预处理完成，处理了 {len(self.combined_texts)} 条记录")
    
    def search(self, query: str, top_k: int = 10, threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """执行精确关键词搜索"""
        if not self.initialized:
            raise ValueError("搜索引擎未初始化，请先调用initialize()方法")
        
        start_time = time.time()
        results = []
        query_lower = query.lower()
        
        for idx, text in enumerate(self.combined_texts):
            text_lower = text.lower()
            original_record = self.data.iloc[idx]
            
            # 计算匹配分数
            score = 0.0
            
            # 1. 精确匹配
            if query_lower in text_lower:
                exact_matches = text_lower.count(query_lower)
                score += 0.6 * min(exact_matches / max(len(query_lower.split()), 1), 1.0)
            
            # 2. 字段特定匹配
            field_bonus = 0.0
            for field, weight in self.field_weights.items():
                if field in original_record and pd.notna(original_record[field]):
                    field_text = str(original_record[field]).lower()
                    if field_text.strip() != '-' and query_lower in field_text:
                        field_bonus += weight
            
            score += 0.4 * min(field_bonus, 1.0)
            
            # 应用阈值过滤
            if threshold is None or score >= threshold:
                if score > 0:
                    results.append({
                        'index': idx,
                        'score': score,
                        'similarity': score,
                        'company_name': original_record.get('公司名称', ''),
                        'address': original_record.get('最新年报地址', ''),
                        'legal_representative': original_record.get('法定代表人', ''),
                        'business_scope': original_record.get('经营范围', ''),
                        'city': original_record.get('所属城市', ''),
                        'district': original_record.get('所属区县', ''),
                        'company_record': original_record.to_dict()
                    })
        
        # 按分数排序
        results.sort(key=lambda x: x['score'], reverse=True)
        
        search_time = time.time() - start_time
        
        return {
            'results': results[:top_k],
            'total_found': len(results),
            'search_time': search_time,
            'method': '精确关键词匹配'
        }


class TFIDFSearchEngine:
    """TF-IDF + 余弦相似度搜索引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.data = None
        self.combined_texts = []
        self.vocabulary = {}
        self.idf_scores = {}
        self.doc_vectors = []
        self.initialized = False

        # 从配置文件读取统一的字段配置
        self._load_field_config()

        # 缓存配置 - 使用混合存储策略
        self.cache_dir = "./cache"
        self.model_file = os.path.join(self.cache_dir, "tfidf_model.pkl")  # 轻量级模型文件
        self.vectors_cache_file = os.path.join(self.cache_dir, "tfidf_vectors.pkl")  # 预计算向量缓存
        self.data_hash_file = os.path.join(self.cache_dir, "tfidf_data_hash.txt")

        # 性能配置
        self.use_vector_cache = True  # 是否使用向量缓存（可配置）
        self.max_cache_size_mb = 500  # 最大缓存大小限制（MB）

    def _load_field_config(self):
        """从配置文件加载字段配置（统一7个字段）"""
        try:
            # 如果config是字典，直接使用；如果是路径，则加载文件
            if isinstance(self.config, str):
                with open(self.config, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
            else:
                config_data = self.config

            multi_field_config = config_data.get('data', {}).get('multi_field', {})

            # 获取主要字段配置
            primary_fields = multi_field_config.get('primary_fields', {})
            # 获取辅助字段配置
            auxiliary_fields = multi_field_config.get('auxiliary_fields', {})
            # 获取传统搜索方法的额外字段配置
            traditional_fields = multi_field_config.get('traditional_search_fields', {})

            self.text_fields = []
            self.field_weights = {}

            # 1. 添加主要字段（公司名称、最新年报地址、法定代表人）
            for field_name, field_config in primary_fields.items():
                self.text_fields.append(field_name)
                if isinstance(field_config, dict):
                    self.field_weights[field_name] = field_config.get('tfidf_weight', 1)
                else:
                    self.field_weights[field_name] = 1

            # 2. 添加辅助字段（登记状态、成立日期）
            if 'status' in auxiliary_fields:
                status_field = auxiliary_fields['status']
                self.text_fields.append(status_field)
                self.field_weights[status_field] = traditional_fields.get(status_field, {}).get('tfidf_weight', 1)

            if 'date' in auxiliary_fields:
                date_field = auxiliary_fields['date']
                self.text_fields.append(date_field)
                self.field_weights[date_field] = traditional_fields.get(date_field, {}).get('tfidf_weight', 1)

            # 3. 添加标识符字段（纳税人识别号、有效手机号）
            if 'identifier' in auxiliary_fields:
                identifier_fields = auxiliary_fields['identifier']
                if isinstance(identifier_fields, list):
                    for id_field in identifier_fields:
                        self.text_fields.append(id_field)
                        self.field_weights[id_field] = traditional_fields.get(id_field, {}).get('tfidf_weight', 1)
                else:
                    self.text_fields.append(identifier_fields)
                    self.field_weights[identifier_fields] = traditional_fields.get(identifier_fields, {}).get('tfidf_weight', 1)

            print(f"   📋 TF-IDF字段 (共{len(self.text_fields)}个): {self.text_fields}")
            print(f"   🔢 字段权重: {self.field_weights}")

        except Exception as e:
            print(f"   ⚠️ 字段配置加载失败，使用默认配置: {e}")
            # 使用默认的7个字段配置
            self.text_fields = ['公司名称', '最新年报地址', '法定代表人', '登记状态', '成立日期', '纳税人识别号', '有效手机号']
            self.field_weights = {
                '公司名称': 3,
                '最新年报地址': 2,
                '法定代表人': 2,
                '登记状态': 1,
                '成立日期': 1,
                '纳税人识别号': 1,
                '有效手机号': 1
            }

    def _get_data_hash(self) -> str:
        """计算数据文件的哈希值"""
        data_file = self.config.get('data_file', './data/浙江所有企业1.xlsx')
        if not os.path.exists(data_file):
            return ""

        # 使用文件修改时间和大小作为简单的哈希
        stat = os.stat(data_file)
        return f"{stat.st_mtime}_{stat.st_size}"

    def _save_model_cache(self):
        """保存TF-IDF模型（混合存储策略）"""
        try:
            os.makedirs(self.cache_dir, exist_ok=True)

            # 1. 保存轻量级模型文件（必需）
            model_data = {
                'vocabulary': self.vocabulary,
                'idf_scores': self.idf_scores,
                'text_fields': self.text_fields,
                'field_weights': self.field_weights,
                'metadata': {
                    'vocab_size': len(self.vocabulary),
                    'doc_count': len(self.combined_texts) if self.combined_texts else 0,
                    'vector_dim': len(self.vocabulary),
                    'created_time': time.time()
                }
            }

            with open(self.model_file, 'wb') as f:
                pickle.dump(model_data, f)

            model_size_mb = os.path.getsize(self.model_file) / (1024 * 1024)
            print(f"   ✅ TF-IDF模型已保存: {self.model_file} ({model_size_mb:.1f}MB)")

            # 2. 可选保存预计算向量缓存
            if self.use_vector_cache and self.doc_vectors:
                vectors_data = {
                    'doc_vectors': self.doc_vectors,
                    'combined_texts': self.combined_texts
                }

                # 检查缓存大小限制
                estimated_size = len(self.doc_vectors) * len(self.vocabulary) * 8 / (1024 * 1024)  # 估算MB

                if estimated_size <= self.max_cache_size_mb:
                    with open(self.vectors_cache_file, 'wb') as f:
                        pickle.dump(vectors_data, f)

                    cache_size_mb = os.path.getsize(self.vectors_cache_file) / (1024 * 1024)
                    print(f"   ✅ 向量缓存已保存: {self.vectors_cache_file} ({cache_size_mb:.1f}MB)")
                else:
                    print(f"   ⚠️ 向量缓存过大({estimated_size:.1f}MB)，跳过缓存保存")

            # 3. 保存数据哈希
            data_hash = self._get_data_hash()
            with open(self.data_hash_file, 'w') as f:
                f.write(data_hash)

        except Exception as e:
            print(f"   ⚠️ 模型保存失败: {e}")

    def _load_model_cache(self) -> bool:
        """从缓存加载TF-IDF模型（混合加载策略）"""
        try:
            if not os.path.exists(self.model_file) or not os.path.exists(self.data_hash_file):
                return False

            # 检查数据是否有变化
            current_hash = self._get_data_hash()
            with open(self.data_hash_file, 'r') as f:
                cached_hash = f.read().strip()

            if current_hash != cached_hash:
                print("   📊 数据文件已更新，需要重新构建模型")
                return False

            # 1. 加载核心模型（必需）
            with open(self.model_file, 'rb') as f:
                model_data = pickle.load(f)

            self.vocabulary = model_data['vocabulary']
            self.idf_scores = model_data['idf_scores']
            self.text_fields = model_data.get('text_fields', self.text_fields)
            self.field_weights = model_data.get('field_weights', self.field_weights)

            metadata = model_data.get('metadata', {})
            model_size_mb = os.path.getsize(self.model_file) / (1024 * 1024)

            print(f"   ✅ 加载TF-IDF模型: {self.model_file} ({model_size_mb:.1f}MB)")
            print(f"   - 词汇表大小: {metadata.get('vocab_size', len(self.vocabulary))}")
            print(f"   - 文档数量: {metadata.get('doc_count', 'unknown')}")

            # 2. 尝试加载预计算向量缓存（可选）
            if self.use_vector_cache and os.path.exists(self.vectors_cache_file):
                try:
                    with open(self.vectors_cache_file, 'rb') as f:
                        vectors_data = pickle.load(f)

                    self.doc_vectors = vectors_data['doc_vectors']
                    self.combined_texts = vectors_data['combined_texts']

                    cache_size_mb = os.path.getsize(self.vectors_cache_file) / (1024 * 1024)
                    print(f"   ⚡ 加载向量缓存: {self.vectors_cache_file} ({cache_size_mb:.1f}MB)")
                    print(f"   - 预计算向量: {len(self.doc_vectors)} 个")

                except Exception as e:
                    print(f"   ⚠️ 向量缓存加载失败，将在搜索时重新计算: {e}")
                    self.doc_vectors = []
                    self.combined_texts = []
            else:
                print("   📝 未使用向量缓存，将在搜索时动态计算")
                self.doc_vectors = []
                self.combined_texts = []

            return True

        except Exception as e:
            print(f"   ⚠️ 模型加载失败: {e}")
            return False

    def initialize(self) -> bool:
        """初始化搜索引擎"""
        try:
            # 加载数据
            data_file = self.config.get('data_file', './data/浙江所有企业1.xlsx')
            print(f"📊 加载企业数据: {data_file}")

            self.data = pd.read_excel(data_file)
            print(f"   - 加载了 {len(self.data)} 条企业记录")

            # 尝试从缓存加载模型
            print("🔧 初始化TF-IDF模型...")
            if self._load_model_cache():
                print("   ⚡ 使用缓存的TF-IDF模型，跳过构建过程")
            else:
                print("   🔨 构建新的TF-IDF模型...")
                # 预处理数据
                self._preprocess_data()

                # 构建TF-IDF模型
                self._build_tfidf_model()

                # 保存到缓存
                self._save_model_cache()

            self.initialized = True
            print("✅ TF-IDF搜索引擎初始化完成")
            return True

        except Exception as e:
            print(f"❌ TF-IDF搜索引擎初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _preprocess_data(self):
        """预处理数据，合并文本字段"""
        self.combined_texts = []

        print("   🔄 预处理企业数据...")
        for _, row in tqdm(self.data.iterrows(), total=len(self.data), desc="处理数据"):
            combined_text = ""
            for field in self.text_fields:
                if field in row and pd.notna(row[field]) and str(row[field]).strip() != '-':
                    field_text = str(row[field]).strip()
                    # 根据字段重要性重复文本
                    weight = self.field_weights.get(field, 1)
                    combined_text += (field_text + " ") * weight
            self.combined_texts.append(combined_text.strip())

        print(f"   ✅ 数据预处理完成，处理了 {len(self.combined_texts)} 条记录")
    
    def _tokenize(self, text: str) -> List[str]:
        """分词"""
        if HAS_JIEBA:
            tokens = list(jieba.cut(text))
        else:
            # 简单分词：按字符和常见分隔符分割
            tokens = re.findall(r'[\u4e00-\u9fa5a-zA-Z0-9]+', text)
        
        # 过滤掉长度小于2的词
        tokens = [token.strip() for token in tokens if len(token.strip()) >= 2]
        return tokens
    
    def _build_vocabulary(self, texts: List[str]):
        """构建词汇表"""
        print("   📚 构建词汇表...")
        word_counts = Counter()
        for text in tqdm(texts, desc="分析词汇"):
            tokens = self._tokenize(text)
            word_counts.update(set(tokens))  # 使用set避免重复计数

        # 过滤低频词
        min_freq = max(2, len(texts) // 1000)
        filtered_words = [word for word, count in word_counts.items() if count >= min_freq]

        self.vocabulary = {word: idx for idx, word in enumerate(filtered_words)}
        print(f"   ✅ 词汇表构建完成，共 {len(self.vocabulary)} 个词汇")
    
    def _calculate_idf(self, texts: List[str]):
        """计算IDF分数"""
        print("   📊 计算IDF分数...")
        doc_count = len(texts)
        word_doc_counts = defaultdict(int)

        for text in tqdm(texts, desc="计算文档频率"):
            tokens = set(self._tokenize(text))
            for token in tokens:
                if token in self.vocabulary:
                    word_doc_counts[token] += 1

        self.idf_scores = {}
        for word in tqdm(self.vocabulary, desc="计算IDF"):
            df = word_doc_counts.get(word, 1)
            self.idf_scores[word] = math.log(doc_count / df)

        print(f"   ✅ IDF计算完成，共 {len(self.idf_scores)} 个词汇")
    
    def _build_tfidf_model(self):
        """构建TF-IDF模型"""
        # 构建词汇表
        self._build_vocabulary(self.combined_texts)
        print(f"   - 词汇表大小: {len(self.vocabulary)}")
        
        # 计算IDF
        self._calculate_idf(self.combined_texts)
        
        # 预计算所有文档的向量
        print("   🔢 计算文档向量...")
        self.doc_vectors = []
        for text in tqdm(self.combined_texts, desc="计算向量"):
            vector = self._text_to_vector(text)
            self.doc_vectors.append(vector)

        print(f"   ✅ 文档向量计算完成，维度: {len(self.vocabulary)}")
    
    def _text_to_vector(self, text: str) -> np.ndarray:
        """将文本转换为TF-IDF向量"""
        tokens = self._tokenize(text)
        tf_counts = Counter(tokens)
        
        vector = np.zeros(len(self.vocabulary))
        
        for word, tf in tf_counts.items():
            if word in self.vocabulary:
                word_idx = self.vocabulary[word]
                if 0 <= word_idx < len(vector):
                    idf = self.idf_scores.get(word, 0)
                    tfidf = tf * idf
                    vector[word_idx] = tfidf
        
        return vector
    
    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算余弦相似度"""
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return np.dot(vec1, vec2) / (norm1 * norm2)
    
    def search(self, query: str, top_k: int = 10, threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """执行TF-IDF搜索（支持动态向量计算）"""
        if not self.initialized:
            raise ValueError("搜索引擎未初始化，请先调用initialize()方法")

        start_time = time.time()

        # 将查询转换为向量
        query_vector = self._text_to_vector(query)

        # 如果没有预计算的文档向量，需要动态计算
        if not self.doc_vectors:
            print("   📝 动态计算文档向量...")
            self._ensure_doc_vectors()

        results = []
        for idx, doc_vector in enumerate(self.doc_vectors):
            similarity = self._cosine_similarity(query_vector, doc_vector)

            # 应用阈值过滤
            if threshold is None or similarity >= threshold:
                if similarity > 0:
                    original_record = self.data.iloc[idx]
                    results.append({
                        'index': idx,
                        'score': similarity,
                        'similarity': similarity,
                        'company_name': original_record.get('公司名称', ''),
                        'address': original_record.get('最新年报地址', ''),
                        'legal_representative': original_record.get('法定代表人', ''),
                        'business_scope': original_record.get('经营范围', ''),
                        'city': original_record.get('所属城市', ''),
                        'district': original_record.get('所属区县', ''),
                        'company_record': original_record.to_dict()
                    })

        # 按相似度排序
        results.sort(key=lambda x: x['similarity'], reverse=True)

        search_time = time.time() - start_time

        return {
            'results': results[:top_k],
            'total_found': len(results),
            'search_time': search_time,
            'method': 'TF-IDF + 余弦相似度'
        }

    def _ensure_doc_vectors(self):
        """确保文档向量已计算（按需计算）"""
        if self.doc_vectors:
            return

        # 如果没有combined_texts，需要重新预处理数据
        if not self.combined_texts:
            self._preprocess_data()

        # 计算文档向量
        self.doc_vectors = []
        for text in self.combined_texts:
            vector = self._text_to_vector(text)
            self.doc_vectors.append(vector)

        print(f"   ✅ 动态计算完成，生成 {len(self.doc_vectors)} 个文档向量")
